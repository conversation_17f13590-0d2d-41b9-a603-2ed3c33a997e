package com.fenbeitong.fenbeipay.awplus.model.dto.huifu;

import java.util.EnumSet;

import org.apache.commons.lang3.StringUtils;

import com.fenbeitong.fenbeipay.core.enums.paycenter.PayStatus;

import lombok.Getter;

/** 
 * <AUTHOR> 
 * @Description: 汇付支付状态枚举
 * 
 * @date 2023-02-14 02:30:23 
*/
@Getter
public enum TransStateEnum {

	PROCESSING("P", "处理中"),
	
	SUCCESS("S", "成功"), 
	
	FAILURE("F", "失败"),
	
	INIT("I", "初始");
	
	String code;
	
	String desc;

	/**
	 * @param code
	 * @param desc
	 */
	private TransStateEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}
	
	/**
	 * 
	 * @param code
	 * @return
	 */
	public static TransStateEnum getByCode(String code) {
		return EnumSet.allOf(TransStateEnum.class).stream().filter(s -> StringUtils.equals(s.getCode(), code)).findFirst().orElseThrow(() -> new RuntimeException("Invalid code" + code));
	}
	
	/**
	 * 翻译成统一的制服状态
	 * @param state
	 * @return
	 */
	public static PayStatus getPayStatus(TransStateEnum state) {
		switch (state) {
		case PROCESSING:
			return PayStatus.TRADEING;
		case SUCCESS:
			return PayStatus.SUCCESS;
		case FAILURE:
			return PayStatus.FAILED;

		default:
			return PayStatus.TRADEING;
		}
	}
	
	/**
	 * 根据汇付支付状态返回统一支付状态
	 * @param code
	 * @return
	 */
	public static PayStatus getPayStatus(String code) {
		TransStateEnum state = getByCode(code);
		return getPayStatus(state);
	}
	
}
