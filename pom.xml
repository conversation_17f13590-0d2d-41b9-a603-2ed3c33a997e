<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fenbeitong</groupId>
    <artifactId>fenbei-pay</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>fenbei-pay-api</module>
        <module>fenbei-pay-core</module>
        <module>fenbei-pay-http</module>
        <module>fenbei-pay-freezen</module>
        <module>fenbei-pay-cashier</module>
        <module>fenbei-pay-vouchers</module>
        <module>fenbei-pay-awplus</module>
        <module>fenbei-pay-account</module>
        <module>fenbei-pay-dto</module>
        <module>fenbei-pay-search</module>
        <module>fenbei-pay-bank</module>
        <module>fenbei-pay-sas</module>
        <module>fenbei-pay-redcoupon</module>
        <module>fenbei-pay-acctpublic</module>
        <module>fenbei-pay-acctdech</module>
        <module>fenbei-pay-extract</module>
        <module>fenbei-pay-common-api</module>
  </modules>

    <properties>
		<pay.api.version>5.3.4.132${current.version}</pay.api.version>
		<dech.api.version>6.2.8.103${current.version}</dech.api.version>
		<guava.version>32.0.0-jre</guava.version>
        <current.version>-SNAPSHOT</current.version>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <compiler.encoding>UTF-8</compiler.encoding>
        <junit.version>4.12</junit.version>
        <spring.version>5.2.12.RELEASE</spring.version>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <jedis.version>2.9.0</jedis.version>
        <slf4j.version>1.7.6</slf4j.version>
        <aspectj.version>1.7.3</aspectj.version>
        <quartz.version>2.3.0</quartz.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <easymock.version>3.6</easymock.version>
        <druid.version>1.0.31</druid.version>
        <com.fasterxml.jackson.version>2.9.8</com.fasterxml.jackson.version>
        <commons.lang.version>2.6</commons.lang.version>
        <finhub.version>1.1.109${current.version}</finhub.version>
        <fenbei.paycore.api.version>1.0.2${current.version}</fenbei.paycore.api.version>
        <fenbei-salary.version>1.2.0${current.version}</fenbei-salary.version>
        <harmony.version>2.0.6${current.version}</harmony.version>
        <biz.card.api.version>1.0.22${current.version}</biz.card.api.version>
        <fenbei-material.version>5.0.18${current.version}</fenbei-material.version>
        <stereo-data-middleplatform.version>1.1.3${current.version}</stereo-data-middleplatform.version>
        <pay.risk.version>1.0.0${current.version}</pay.risk.version>
        <budget-service-api.version>2.6.0${current.version}</budget-service-api.version>
        <fx.pay.api>5.2.10.102${current.version}</fx.pay.api>
        <fx.card.api>1.0.0${current.version}</fx.card.api>
        <uc.api.version>5.3.54${current.version}</uc.api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-api</artifactId>
                <version>${pay.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-dto</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-bank</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-sas</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-core</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-freezen</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-awplus</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-account</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-vouchers</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-cashier</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-search</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-redcoupon</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-acctpublic</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-extract</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-pay-acctdech</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <!--二方包-->
            <dependency>
                <groupId>com.fenbeitong.config</groupId>
                <artifactId>fenbei-config-api</artifactId>
                <version>1.0.3${current.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>finhub-validator</artifactId>
                        <groupId>com.finhub.framework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-common</artifactId>
                <version>${finhub.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <version>${dech.api.version}</version>
                <artifactId>fenbei-dech-api</artifactId>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <version>6.0.3.0602${current.version}</version>
                <artifactId>fenbei-acctperson-api</artifactId>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-base</artifactId>
                <version>${finhub.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-dubbo</artifactId>
                <version>${finhub.version}</version>
            </dependency>
            <dependency>
                <groupId>com.luastar</groupId>
                <artifactId>swift-tools</artifactId>
                <version>1.0.4-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bson</artifactId>
                        <groupId>org.mongodb</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.luastar</groupId>
                <artifactId>swift-i18n</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>usercenter-api</artifactId>
                <version>${uc.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>alipay-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-kafka</artifactId>
                <version>${finhub.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>saas-plus-api</artifactId>
                <version>*********${current.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--活动-->
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-activity-api</artifactId>
                <version>4.5.1${current.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>eventbus_2.11</artifactId>
                <version>1.0.143-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-core</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mongodb</groupId>
                        <artifactId>bson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-common-oss</artifactId>
                <version>${finhub.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-data-commons</artifactId>
                        <groupId>org.springframework.data</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-data-redis</artifactId>
                        <groupId>org.springframework.data</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.5.0</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>meta-sdk</artifactId>
                <version>2.0.3${current.version}</version>
            </dependency>

			<dependency>
			    <groupId>com.github.pagehelper</groupId>
	            <artifactId>pagehelper-spring-boot-starter</artifactId>
	            <version>1.3.0</version>
			</dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>5.2</version>
            </dependency>

            <!-- http工具 start -->
            <dependency>
                <groupId>com.jakewharton.retrofit</groupId>
                <artifactId>retrofit2-rxjava2-adapter</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>2.4.0</version>
            </dependency>


            <!--DB-->
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-mongodb</artifactId>
                <version>2.2.3.RELEASE</version>
            </dependency>
            <!--<dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>3.12.0</version>
            </dependency>-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!-- 排除默认的 LogBack 组件-->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!-- 排除默认的 LogBack 组件-->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!-- 排除默认的 LogBack 组件-->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.jodah</groupId>
                <artifactId>expiringmap</artifactId>
                <version>0.5.8</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>4.0.4</version>
            </dependency>

            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-generator</artifactId>
                <version>1.0.4</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>1.3.6</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.12.5</version>
                <!--     跟dubbo netty冲突排除4.1.48         -->
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-buffer</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-handler</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-resolver</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-dns</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-resolver-dns</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-21</artifactId>
                <version>3.12.5</version>
            </dependency>
            <!--DB end-->

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-auth</artifactId>
                <version>1.1.37${current.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.20</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>5.3.4.Final</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.web</groupId>
                <artifactId>javax.el</artifactId>
                <version>2.2.4</version>
            </dependency>

            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>3.0.0</version>
            </dependency>

            <!--分页插件-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.9</version>
            </dependency>

            <!--JSON+COMMON-->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${com.fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${com.fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-guava</artifactId>
                <version>${com.fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${com.fasterxml.jackson.version}</version>
            </dependency>

            <!--分布式+微服务-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo</artifactId>
                <version>2.6.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-expression</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.13</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.101tec</groupId>
                <artifactId>zkclient</artifactId>
                <version>0.10</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>4.0.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>harmony-third-examine-api</artifactId>
                <version>2.0.9${current.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--三方支付-->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.10.159.ALL</version>
            </dependency>

            <dependency>
			    <groupId>com.github.wechatpay-apiv3</groupId>
			    <artifactId>wechatpay-java-core</artifactId>
			    <version>0.2.5</version>
			</dependency>

			<dependency>
				<groupId>com.huifu.bspay.sdk</groupId>
				<artifactId>dg-java-sdk</artifactId>
				<version>3.0.5</version>
			</dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>${easymock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-bank-api</artifactId>
                <version>5.4.2.23${current.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-settlement-external-api</artifactId>
                <version>2.1.0${current.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.26</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong.bank.ent</groupId>
                <artifactId>fenbei-bank-ent-api</artifactId>
                <version>5.2.8.053${current.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.finhub.framework</groupId>
                        <artifactId>finhub-dubbo</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.finhub.framework</groupId>
                        <artifactId>finhub-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>stereo-api</artifactId>
                <version>2.0.5${current.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fenbei.paycore</groupId>
                <artifactId>paycore-api</artifactId>
                <version>${fenbei.paycore.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.salary</groupId>
                <artifactId>fenbei-salary-api</artifactId>
                <version>${fenbei-salary.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.finhub.framework</groupId>
                        <artifactId>finhub-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>harmony-captcha-api</artifactId>
                <version>${harmony.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>biz-card-api</artifactId>
                <version>${biz.card.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>dubbo</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbei.material</groupId>
                <artifactId>material-api</artifactId>
                <version>${fenbei-material.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>4.8.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-tcnative-boringssl-static</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>stereo-data-middleplatform-api</artifactId>
                <version>${stereo-data-middleplatform.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.pay.risk</groupId>
                <artifactId>fenbei-pay-risk-api</artifactId>
                <version>${pay.risk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>budget-service-api</artifactId>
                <version>${budget-service-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong.fxpay</groupId>
                <artifactId>fenbei-fx-pay-api</artifactId>
                <version>${fx.pay.api}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbei.fx.card</groupId>
                <artifactId>fenbei-fx-card-api</artifactId>
                <version>${fx.card.api}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 插件配置 -->
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <annotationProcessorPaths>
			              <path>
			                <groupId>org.mapstruct</groupId>
			                <artifactId>mapstruct-processor</artifactId>
			                <version>1.4.2.Final</version>
			              </path>
			              <path>
			                <groupId>org.projectlombok</groupId>
			                <artifactId>lombok</artifactId>
			                <version>1.18.20</version>
			              </path>
			              <path>
			                <groupId>org.projectlombok</groupId>
			                <artifactId>lombok-mapstruct-binding</artifactId>
			                <version>0.2.0</version>
			              </path>
			            </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <warName>${project.artifactId}</warName>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.6</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>nexus-releases</name>
            <url>http://nexus.fenbeijinfu.com:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://nexus.fenbeijinfu.com:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>snapshots</id>
            <url>http://nexus.fenbeijinfu.com:8081/nexus/content/repositories/snapshots/</url>
            <name>nexus-snapshots</name>
            <snapshots>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <!-- 针对不同运行环境的配置 -->
    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <profile.name>local</profile.name>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.name>dev</profile.name>
                <current.version>-SNAPSHOT</current.version>
            </properties>
        </profile>
        <profile>
            <id>dev-dingtalk</id>
            <properties>
                <profile.name>dev-dingtalk</profile.name>
            </properties>
        </profile>
        <profile>
            <id>dd-fat</id>
            <properties>
                <profile.name>dd-fat</profile.name>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <profile.name>staging</profile.name>
            </properties>
        </profile>
        <profile>
            <id>fat2</id>
            <properties>
                <profile.name>fat2</profile.name>
            </properties>
        </profile>
        <profile>
            <id>js-fat3</id>
            <properties>
                <profile.name>js-fat3</profile.name>
            </properties>
        </profile>
        <profile>
            <id>ali-uat</id>
            <properties>
                <profile.name>ali-uat</profile.name>
                <current.version>.RELEASE</current.version>
            </properties>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <profile.name>release</profile.name>
                <current.version>.RELEASE</current.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profile.name>prod</profile.name>
                <current.version>.RELEASE</current.version>
            </properties>
        </profile>
        <profile>
            <id>pro-dingtalk</id>
            <properties>
                <profile.name>pro-dingtalk</profile.name>
                <current.version>.RELEASE</current.version>
            </properties>
        </profile>
        <profile>
            <id>staging-b</id>
            <properties>
                <profile.name>staging-b</profile.name>
            </properties>
        </profile>
        <profile>
            <id>prod-b</id>
            <properties>
                <profile.name>prod-b</profile.name>
                <current.version>.RELEASE</current.version>
            </properties>
        </profile>

        <profile>
            <id>tx-prod-a</id>
            <properties>
                <profile.name>tx-prod-a</profile.name>
                <current.version>.RELEASE</current.version>
            </properties>
        </profile>

        <profile>
            <id>tx-prod-b</id>
            <properties>
                <profile.name>tx-prod-b</profile.name>
                <current.version>.RELEASE</current.version>
            </properties>
        </profile>
    </profiles>
</project>
