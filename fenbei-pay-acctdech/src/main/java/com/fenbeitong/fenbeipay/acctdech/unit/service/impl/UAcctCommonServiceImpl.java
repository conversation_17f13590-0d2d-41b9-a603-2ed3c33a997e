package com.fenbeitong.fenbeipay.acctdech.unit.service.impl;

import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.CREDIT;
import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.RECHARGE;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.ACCOUNT_RECEIPT_BANK_ERROR;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.DOWNLOAD_RECEIPT_COUNT_OVER_LIMIT;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.EXCEPTION;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.MAIN_COMPANY_NO_EXIST;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.REDIS_GET_LOCK_ERROR;
import static com.fenbeitong.finhub.common.constant.FundAcctActStatusEnum.ACTIVATE;
import static com.fenbeitong.finhub.common.constant.FundAcctActStatusEnum.UN_ACTIVATE;
import static com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum.SHOW;
import static com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum.UN_SHOW;
import com.fenbei.fx.card.api.card.ICardService;
import com.fenbei.fx.card.api.card.dto.CardBalanceRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.CardBalanceRpcRespDTO;
import com.fenbeitong.bank.api.utils.CopyUtils;
import com.fenbeitong.fenbeipay.api.util.BankNameShowConfig;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.fxpay.api.enums.FxCompanyAccountSubType;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.RepaymentNocReqDTO;
import com.fenbeitong.acctperson.api.service.trade.IBankUserCardTradeService;
import com.fenbeitong.bank.api.model.BankPublicAccountInfoReqDTO;
import com.fenbeitong.bank.api.service.IBankPublicAccountInfoService;
import com.fenbeitong.bank.ent.api.account.BankEntAccountInfoService;
import com.fenbeitong.bank.ent.api.account.dto.BankEntAccountInfoDTO;
import com.fenbeitong.bank.ent.api.account.dto.BankEntAccountInfoReqDTO;
import com.fenbeitong.bank.ent.api.account.dto.BankEntAccountInfoRespDTO;
import com.fenbeitong.bank.ent.enums.BankEntPlatformCodeEnum;
import com.fenbeitong.dech.api.enums.AccountStatusEnum;
import com.fenbeitong.dech.api.enums.CustomerTypeEnum;
import com.fenbeitong.dech.api.model.dto.*;
import com.fenbeitong.dech.api.model.dto.lf.LfTradeReceiptRespDTO;
import com.fenbeitong.dech.api.service.ISpdSearchService;
import com.fenbeitong.fenbeipay.account.dto.GroupTransferWhitelist;
import com.fenbeitong.fenbeipay.acctdech.conver.*;
import com.fenbeitong.fenbeipay.acctdech.enums.MergePdfEnum;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyBindCardService;
import com.fenbeitong.fenbeipay.acctdech.service.BankAcctLimitService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.utils.ExportBillExcelUtil;
import com.fenbeitong.fenbeipay.acctpublic.manager.AcctPublicFlowManager;
import com.fenbeitong.fenbeipay.api.constant.enums.account.*;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.AccountPublicStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.CostImageStatusMapping;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCreditMainDTO.SimpleCreditAcct;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.BankAcctDetailDTO.SimpleDebitAcctDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.dech.req.AcctPublicCreateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwAcctRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountChangeNameReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubAbleReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.bank.BankAcctLimitReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponInfoVO;
import com.fenbeitong.fenbeipay.core.common.base.ExportElectronicTaskDTO;
import com.fenbeitong.fenbeipay.core.common.base.PayContext;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.constant.personpay.CompanySwitchConstant;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.enums.account.AccountOptTypeEnum;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.AcctFlowBaseDTO;
import com.fenbeitong.fenbeipay.core.model.dto.BillFlowPageQuery;
import com.fenbeitong.fenbeipay.core.model.vo.account.*;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyAccountInfo.AcctAuthEnum;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyAccountInfo.AcctAuthTypeEnum;
import com.fenbeitong.fenbeipay.core.service.bigdata.AccountBillFlowService;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.core.utils.VersionTool;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.bank.BankAcctLimitFlow;
import com.fenbeitong.fenbeipay.dto.gw.AcctCompanyGateway;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezen;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezenFlow;
import com.fenbeitong.fenbeipay.dto.oversea.AcctOversea;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.dech.KafkaDechTradeResultMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaBankConsumeTaskMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaBankRefundTaskMsg;
import com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService;
import com.fenbeitong.fxpay.api.vo.CompanyAcctRes;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctInfoReq;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountConvertDTO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountOperateDTO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyVirtualCardDTO;
import com.fenbeitong.usercenter.api.model.dto.company.GroupCompany;
import com.fenbeitong.usercenter.api.model.enums.company.EffectiveAccountSourceEnum;
import com.fenbeitong.usercenter.api.service.company.IRCompanyService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.MoneyUtils;

import cn.hutool.core.util.RandomUtil;

/**
 * <AUTHOR>
 * @Date 2020/12/22
 * @Description
 */
@Service
public class UAcctCommonServiceImpl extends UAcctAbstractService implements UAcctCommonService {
    /**
     * 强制解锁时间设置
     */
    private static final long LOCK_TIME_REFUND = 10000L;
    /**
     * 电子回单下载数量限制
     */
    private static final int DOWNLOAD_RECEIPT_COUNT_LIMIT = 2000;
    /**
     * 等待时间
     **/
    private static final long WAIT_TIME_REFUND = 0L;
    /**
     * 合并pdf压缩阈值
     */
    private static final int MERGER_PDF_LIMIT = 100;

    private static final String REDIS_UN_LOCK_ERROR ="【4.0账户系统异常】redis解锁异常";

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private IRCompanyService iRCompanyService;

    @Autowired
    private AcctPublicFlowManager acctPublicFlowManager;

    @Autowired
    private BankEntAccountInfoService bankEntAccountInfoService;
    
    @Autowired
    private AcctCompanyBindCardService acctCompanyBindCardService;

    @Autowired
    private IBankPublicAccountInfoService iBankPublicAccountInfoService;

    @Autowired
    private IBankUserCardTradeService iBankUserCardTradeService;

    @Autowired
    private AccountBillFlowService accountBillFlowService;

    @Autowired
    private BankAcctLimitService bankAcctLimitService;

    @Autowired
    private ICompanyAcctService iCompanyAcctService;
        
    @Autowired
    private ISpdSearchService iSpdSearchService;

    @Autowired
    private ICardService iCardService;

    private static final String OSS_BIZ_CODE = "publicpay";

    private static final Integer RECEIPT_TASK_CATEGORY = 100;

    private static final String RECEIPT_TASK_CATEGORY_NAME = "银行交易流水下载";

    public static final String ZX_UPDATE_REDIS_KEY = "ZX_UPDATE_ACCOUNT_";

    public static final String ZX_SHOW_MAIN_NAME_WHITE_LIST_REDIS_KEY = "ZX_SHOW_MAIN_NAME_WHITE_LIST";

    @Override
    public void createAccountSubByFbt(AcctCreateFbtReqDTO createFbtReqDTO, AcctCreateSubDTO acctCreateSubDTO, CompanyPlatformAccountConvertDTO comPlatAccListDe, CompanyPlatformAccountConvertDTO comPlatAccListCr) {
        acctCreateSubDTO.setCompanyMainType(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey());
        acctCreateSubDTO.setCompanyMainId(acctCreateSubDTO.getCompanyId());
        acctCreateSubDTO.setBankAcctId(acctCreateSubDTO.getCompanyId());
        acctCreateSubDTO.setBankAccountNo(acctCreateSubDTO.getCompanyId());
        if (FundAccountSubType.isBusinessAccount(acctCreateSubDTO.getAccountSubType())) {
            acctCreateSubDTO.setInitCredit(createFbtReqDTO.getInitCredit());
            createBussAccountByFbt(acctCreateSubDTO, comPlatAccListDe, comPlatAccListCr);
        } else if (FundAccountSubType.isIndividualAccount(acctCreateSubDTO.getAccountSubType())) {
            createIndiAccountByFbt(acctCreateSubDTO, comPlatAccListDe, comPlatAccListCr);
        }
    }

    @Override
    public void createAccountSubByFbtAuthBefore(AcctCreateFbtReqDTO createFbtReqDTO, AcctCreateSubDTO acctCreateSubDTO, CompanyPlatformAccountConvertDTO comPlatAccListDe, CompanyPlatformAccountConvertDTO comPlatAccListCr) {
        acctCreateSubDTO.setCompanyMainType(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey());
        acctCreateSubDTO.setCompanyMainId(acctCreateSubDTO.getCompanyId());
        acctCreateSubDTO.setBankAcctId(acctCreateSubDTO.getCompanyId());
        acctCreateSubDTO.setBankAccountNo(acctCreateSubDTO.getCompanyId());
        if (FundAccountSubType.isBusinessAccount(acctCreateSubDTO.getAccountSubType())) {
            acctCreateSubDTO.setInitCredit(createFbtReqDTO.getInitCredit());
            createBussAccountByFbtAuthBefore(acctCreateSubDTO, comPlatAccListDe, comPlatAccListCr);
        } else if (FundAccountSubType.isIndividualAccount(acctCreateSubDTO.getAccountSubType())) {
            createIndiAccountByFbtAuthBefore(acctCreateSubDTO, comPlatAccListDe, comPlatAccListCr);
        }
    }
    
    @Override
    public void createOverseaAccount(String accountGeneralId, AcctCreateBankReqDTO createBankReqDTO, CompanyPlatformAccountConvertDTO comPlatAccList) {
    	if (Objects.isNull(comPlatAccList)) {
    		return;
    	}
        AcctCreateSubDTO acctCreateSub = new AcctCreateSubDTO();
        BeanUtils.copyProperties(createBankReqDTO, acctCreateSub);
        acctCreateSub.setAccountGeneralId(accountGeneralId);
        acctCreateSub.setAccountModel(FundAccountModelType.RECHARGE.getKey());
        acctCreateSub.setActiveStatus(ACTIVATE.getStatus());
        createAcctOversea(comPlatAccList, acctCreateSub);
    }

    @Override
    public void createAccountSubByBankDebit(String accountGeneralId, AcctCreateBankReqDTO createBankReqDTO, CompanyPlatformAccountConvertDTO platformAccountListDTO) {
        if (Objects.nonNull(platformAccountListDTO)) {
            //模式
            Integer accountModel = Objects.nonNull(platformAccountListDTO.getAccountType()) ? platformAccountListDTO.getAccountType() : FundAccountModelType.RECHARGE.getKey();
            AcctCreateSubDTO acctCreateSubDTO = new AcctCreateSubDTO();
            BeanUtils.copyProperties(createBankReqDTO, acctCreateSubDTO);
            acctCreateSubDTO.setAccountGeneralId(accountGeneralId);
            acctCreateSubDTO.setAccountModel(accountModel);
            acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
            if (FundAccountModelType.isRecharge(accountModel)) {
                //创建充值
                //创建商务
                createAcctBusiness(platformAccountListDTO, acctCreateSubDTO);
                //个人
                createAcctIndividual(platformAccountListDTO, acctCreateSubDTO);
                //虚拟卡
                createAcctCompanyCard(createBankReqDTO, platformAccountListDTO, acctCreateSubDTO);
                //对公付款
                createAcctPublic(createBankReqDTO, platformAccountListDTO, acctCreateSubDTO);
                //员工报销账户
                createAcctReimbursement(createBankReqDTO, platformAccountListDTO, acctCreateSubDTO);
            }
        }
    }

    @Override
    public void createAccountSubByBankCredit(String accountGeneralId, AcctCreateBankReqDTO createBankReqDTO, CompanyPlatformAccountConvertDTO platformAccountListDTO) {
        if (Objects.nonNull(platformAccountListDTO)) {
            //模式
            Integer accountModel = Objects.nonNull(platformAccountListDTO.getAccountType()) ? platformAccountListDTO.getAccountType() : FundAccountModelType.CREDIT.getKey();
            AcctCreateSubDTO acctCreateSubDTO = new AcctCreateSubDTO();
            BeanUtils.copyProperties(createBankReqDTO, acctCreateSubDTO);
            acctCreateSubDTO.setAccountGeneralId(accountGeneralId);
            BeanUtils.copyProperties(createBankReqDTO, acctCreateSubDTO);
            acctCreateSubDTO.setAccountModel(accountModel);
            acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
            if (FundAccountModelType.isCredit(accountModel)) {
                //创建授信
                //商务
                Integer businessConsume = platformAccountListDTO.getBusinessConsume();
                if (FundAcctStatusEnum.isKnow(businessConsume)) {
                    acctCreateSubDTO.setAccountStatus(businessConsume);
                    acctCreateSubDTO.setShowStatus(FundAcctStatusEnum.isEnable(businessConsume) ? SHOW.getStatus() : UN_SHOW.getStatus());
                    uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
                }
                //个人
                Integer personalConsume = platformAccountListDTO.getPersonalConsume();
                if (FundAcctStatusEnum.isKnow(personalConsume)) {
                    acctCreateSubDTO.setAccountStatus(personalConsume);
                    acctCreateSubDTO.setShowStatus(FundAcctStatusEnum.isEnable(personalConsume) ? SHOW.getStatus() : UN_SHOW.getStatus());
                    uAcctIndividualCreditService.establishAccount(acctCreateSubDTO);
                }
            }
        }
    }

    /**
     * 创建结算账户
     * @param createBankReqDTO
     */
    @Override
    public void creatAcctSettlementByBank(String accountGeneralId, AcctCreateBankReqDTO createBankReqDTO) {
        if(BankNameEnum.isSpa(createBankReqDTO.getBankName())){
            AcctCreateSubDTO acctCreateSubDTO = new AcctCreateSubDTO();
            BeanUtils.copyProperties(createBankReqDTO, acctCreateSubDTO);
            acctCreateSubDTO.setAccountGeneralId(accountGeneralId);
            acctCreateSubDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            acctCreateSubDTO.setShowStatus(UN_SHOW.getStatus());
            AcctPublicCreateReqDTO reqDTO = new AcctPublicCreateReqDTO();
            BeanUtils.copyProperties(acctCreateSubDTO, reqDTO);
            BeanUtils.copyProperties(createBankReqDTO, reqDTO);
            uAcctSettlementService.createAccount(acctCreateSubDTO);
        }
    }

    @Override
    public AcctOverviewRespDTO queryAcctOverview(AcctOverviewReqDTO reqDTO) {
        String companyId = reqDTO.getCompanyId();
        AcctOverviewRespDTO respDTO = new AcctOverviewRespDTO(companyId);
        if(StringUtils.isBlank(companyId)){
            return respDTO;
        }
        List<AcctDebitMainRespDTO> acctDebitMainList = new ArrayList<>();
        List<AcctCreditMainRespDTO> acctCreditMainList = new ArrayList<>();

        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return respDTO;
        }
        //员工报销账户
        List<AcctReimbursement> acctReimbursements = uAcctReimbursementService.findByCompanyId(companyId);
        //平安银行账户下“待结算金额”隐藏不展示ZHIFU-8438
        List<AcctSettlement> acctSettlements = uAcctSettlementService.findByCompanyId(companyId);
        List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findByCompanyId(companyId);
        List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findByCompanyId(companyId);
        List<AcctBusinessCredit> businessCredits = uAcctBusinessCreditService.findByCompanyId(companyId);
        List<AcctIndividualCredit> individualCredits = uAcctIndividualCreditService.findByCompanyId(companyId);
        List<AcctCompanyCard> acctCompanyCards = uAcctCompanyCardService.findByCompanyId(companyId);
        List<AcctPublicDetailRespDTO> publicRespDtos = acctPublicSearchService.findByCompanyId(companyId);
        // 查询海外卡
        List<AcctOversea> acctOverseaList = acctOverseaService.queryByCompanyId(companyId);
        initOverseaAcctBalance(acctOverseaList);
        Integer accountSubType = FundAccountSubType.BUSINESS_ACCOUNT.getKey();
        boolean companySwitch = uCompanySwitchService.isCompanySwitch(companyId);
        //不在白名单 是商务账户 在白名单 在区分平台方
        if (companySwitch) {
            accountSubType = FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey();
        }
        List<FundFreezen> fundFreezens = uFundFreezenService.queryByCompanyIdAndUseTypes(companyId, accountSubType, Arrays.asList(FundAccountModelType.RECHARGE.getKey(), FundAccountModelType.CREDIT.getKey()), FreezenUseType.bankFreezenInt());
        List<AccountGeneral> fbtGeneral = accountGenerals.stream().filter(s -> BankNameEnum.isFbt(s.getBankName())).collect(Collectors.toList());
        String companyName = fbtGeneral.get(0).getCompanyName();
        accountGenerals.stream().filter(Objects::nonNull).forEach(general -> {
            //充值主体
            AcctDebitMainRespDTO debitMainRespDTO = AcctMainConvert.buildAcctDebitMainRespDTO(businessDebits, individualDebits, acctCompanyCards, publicRespDtos, fundFreezens, general, acctSettlements, acctReimbursements, acctOverseaList);
            // 中信是否升级
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(general.getCompanyId(), general.getCompanyMainId(), general.getBankName());
            // 如果主体信息中bank_business_name字段为空证明未升级，展示按钮
            if (Objects.nonNull(acctCompanyMain) && StringUtils.isBlank(acctCompanyMain.getBankBusinessName()) && BankNameEnum.isCitic(general.getBankName())) {
                debitMainRespDTO.setUpdateZxAcctShow(true);
            }
            // 如果是本主体，且主体名称不是企业名称，证明需要切换成其他主体
            if (Objects.nonNull(acctCompanyMain) && CompanyMainTypeEnum.isMainSelf(acctCompanyMain.getCompanyMainType()) && !acctCompanyMain.getBusinessName().equals(companyName)) {
                debitMainRespDTO.setSwitchMainShow(true);
            }

            if (Objects.nonNull(debitMainRespDTO)) {
                acctDebitMainList.add(debitMainRespDTO);
            }

            // 构建授信主体
            buildAcctCreditMainList(acctCreditMainList, businessCredits, individualCredits, general);
        });
        BigDecimal debitAllBalance = acctDebitMainList.stream().filter(Objects::nonNull).map(AcctDebitMainRespDTO::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<AcctDebitMainRespDTO> acctDebitMainListSort = acctDebitMainRespSort(acctDebitMainList);
        //红包券
        AccountRedcouponInfoVO redCouponInfo = accountRedcouponManager.queryAccountCouponInfoVO(companyId);
        respDTO.setCompanyId(companyId);
        respDTO.setAllBalance(debitAllBalance);
        respDTO.setAcctDebitMainRespDTOList(acctDebitMainListSort);
        respDTO.setAcctCreditMainRespDTOList(acctCreditMainList);
        respDTO.setAccountRedcouponInfoVO(redCouponInfo);

        // 外币账户额度处理
        List<FxCompanyAcctInfos> fxCompanyAcctInfos = fxCompanyAcctBuild(companyId);
        if (CollectionUtils.isNotEmpty(fxCompanyAcctInfos)) {
            BigDecimal fxAllUsdAmount = BigDecimal.ZERO;
            for (FxCompanyAcctInfos fxCompanyAcctInfo : fxCompanyAcctInfos) {
                if (Objects.nonNull(fxCompanyAcctInfo.getTotalUsdBalance())) {
                    fxAllUsdAmount = fxAllUsdAmount.add(fxCompanyAcctInfo.getTotalUsdBalance());
                }
            }
            respDTO.setFxCompanyAcctInfosList(fxCompanyAcctInfos);
            respDTO.setAllUsdBalanceShow(MoneyUtils.formatYuan(MoneyUtils.fen2yuan(fxAllUsdAmount), true, false));
        }

        return respDTO;
    }
    
    /**
     * 初始化海外卡账户余额
     * @param acctOverseaList
     */
    private void initOverseaAcctBalance(List<AcctOversea> acctOverseaList) {
    	if (CollectionUtils.isEmpty(acctOverseaList)) {
    		return;
    	}
    	AcctOversea acctOversea = acctOverseaList.get(0);
        CardBalanceRpcReqDTO cardReq = new CardBalanceRpcReqDTO();
        cardReq.setCompanyId(acctOversea.getCompanyId());
        cardReq.setPlatform(FxAcctChannelEnum.LIANLIAN.getChannel());
        try {
            List<CardBalanceRpcRespDTO> cardRes = iCardService.getBalance(cardReq);
            if (CollectionUtils.isEmpty(cardRes)) {
            	return;
            }
            cardRes.stream().filter(card -> StringUtils.equalsIgnoreCase(acctOversea.getCurrency(), card.getCurrency()))
            	.filter(card -> Objects.nonNull(card.getAllBalance()))
            	.findFirst()
            	.ifPresent(card -> {
            		acctOversea.setVccBalance(card.getAllBalance());
            	});
        } catch (Exception e) {
            FinhubLogger.warn("获取虚拟卡信息异常iCardService error cardReq:{}", JSON.toJSONString(cardReq), e);
        }
    }

    @Override
    public AcctGroupOverviewRespDto queryGroupAcctOverview(String groupId) {
        List<GroupCompany> groupCompanies = iRCompanyService.listGroupCompanyInfo(groupId);
        AcctGroupOverviewRespDto respDto = new AcctGroupOverviewRespDto();
        if (CollectionUtils.isEmpty(groupCompanies)) {
            return respDto;
        }

        List<AcctGroupCompanyRespDto> groupAcctDataList = new ArrayList<>();
        BigDecimal totalBalance = BigDecimal.ZERO;
        BigDecimal allCredit = BigDecimal.ZERO;
        BigDecimal allCreditBalance = BigDecimal.ZERO;
        for (GroupCompany company : groupCompanies) {
            AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(company.getId());
            AcctOverviewRespDTO acctOverviewRespDTO = queryAcctOverview(reqDTO);

            AcctGroupCompanyRespDto acctGroupCompanyRespDto = new AcctGroupCompanyRespDto();
            BeanUtils.copyProperties(acctOverviewRespDTO, acctGroupCompanyRespDto);
            acctGroupCompanyRespDto.setCompanyAcctShowName(company.getName());
            acctGroupCompanyRespDto.setCompanyGroupType(company.getCompanyType());
            groupAcctDataList.add(acctGroupCompanyRespDto);
            totalBalance = totalBalance.add(acctOverviewRespDTO.getAllBalance());

            List<AcctCreditMainRespDTO> acctCreditMainRespDTOList = acctOverviewRespDTO.getAcctCreditMainRespDTOList();
            if (CollectionUtils.isNotEmpty(acctCreditMainRespDTOList)) {
                for (AcctCreditMainRespDTO creditMainRespDTO : acctCreditMainRespDTOList) {
                    allCredit = allCredit.add(creditMainRespDTO.getTotalCredit());
                    allCreditBalance = allCreditBalance.add(creditMainRespDTO.getTotalCreditBalance());
                }
            }
        }
        groupAcctDataList = groupAcctDataList.stream().sorted(Comparator.comparing(AcctGroupCompanyRespDto::getCompanyGroupType)).collect(Collectors.toList());
        respDto.setGroupAcctDataList(groupAcctDataList);
        respDto.setAllBalance(totalBalance);
        respDto.setAllCredit(allCredit);
        respDto.setAllCreditBalance(allCreditBalance);
        return respDto;
    }

    private List<AcctDebitMainRespDTO> acctDebitMainRespSort(List<AcctDebitMainRespDTO> acctDebitMainList) {

        //调用sort()方法，并实现Comparator接口中的compare()方法
        Collections.sort(acctDebitMainList, Comparator.comparingInt(e -> getSortOrder(e)));
        return acctDebitMainList;
    }

    /**
     * 硬性排序,规则是固定的
     * @param e
     * @return
     */
    private int getSortOrder(AcctDebitMainRespDTO e){
        if (Objects.nonNull(e.getBussDebitDetailRespDTO()) && e.getBussDebitDetailRespDTO().isActive()) {
            return 1;
        } else if (Objects.nonNull(e.getIndlDebitDetailRespDTO()) && e.getIndlDebitDetailRespDTO().isActive()) {
            return 2;
        } else if (Objects.nonNull(e.getCompanyCardRespDTO()) && e.getCompanyCardRespDTO().isActive()) {
            return 3;
        } else if (Objects.nonNull(e.getPublicDetailRespDTO())) {
            if (CompanyMainTypeEnum.isMainSelf(e.getCompanyMainType())) {
                return 4;
            } else {
                return 5;
            }
        } else {
            return 6;
        }
    }
    @Override
    public AllAcctByCompanyModelRespDTO allAcctByCompanyModel(AcctByCompanyModelReqDTO reqDTO) {
        String companyId = reqDTO.getCompanyId();
        AllAcctByCompanyModelRespDTO respDTO = new AllAcctByCompanyModelRespDTO(companyId, reqDTO.getUpdateCompanyModel());
        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return respDTO;
        }
        List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findByCompanyId(companyId);
        List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findByCompanyId(companyId);
        List<AcctBusinessCredit> businessCredits = uAcctBusinessCreditService.findByCompanyId(companyId);
        List<AcctIndividualCredit> individualCredits = uAcctIndividualCreditService.findByCompanyId(companyId);
        List<AcctCompanyCard> acctCompanyCards = uAcctCompanyCardService.findByCompanyId(companyId);
        // TODO 海外卡是否涉及合作模式
//        List<AcctOversea> acctOverseas = acctOverseaService.queryByCompanyId(companyId);
        
        List<AcctDebitByCompanyModelRespDTO> acctDebitMainList = new ArrayList<>();
        List<AcctCreditByCompanyModelRespDTO> acctCreditMainList = new ArrayList<>();
        for (AccountGeneral accountGeneral : accountGenerals) {
            //充值主体
            AcctDebitByCompanyModelRespDTO debitAllRespDTO = AcctMainConvert.buildAcctDebitByCompanyModelRespDTO(businessDebits, individualDebits, acctCompanyCards, accountGeneral, reqDTO);
            if (Objects.nonNull(debitAllRespDTO)) {
                acctDebitMainList.add(debitAllRespDTO);
            }
            //授信主体
            AcctCreditByCompanyModelRespDTO creditMainRespDTO = AcctMainConvert.makeAcctCreditByCompanyModelRespDTO(businessCredits, individualCredits, accountGeneral, reqDTO);
            if (Objects.nonNull(creditMainRespDTO)) {
                acctCreditMainList.add(creditMainRespDTO);
            }
        }
        respDTO.setAcctDebitMainRespDTOList(acctDebitMainList);
        respDTO.setAcctCreditMainRespDTOList(acctCreditMainList);
        return respDTO;
    }

    @Override
    public AcctOverviewRespDTO queryWebAcctOverview(AcctOverviewReqDTO reqDTO) {
        String companyId = reqDTO.getCompanyId();
        AcctOverviewRespDTO respDTO = new AcctOverviewRespDTO(companyId);
        if(StringUtils.isBlank(companyId)){
            return respDTO;
        }
        List<AcctDebitMainRespDTO> acctDebitMainList = new ArrayList<>();
        List<AcctCreditMainRespDTO> acctCreditMainList = new ArrayList<>();

        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return respDTO;
        }

        BigDecimal allBalance = BigDecimal.ZERO;
        //员工报销账户
        List<AcctReimbursement> acctReimbursements = uAcctReimbursementService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(acctReimbursements)) {
            BigDecimal reimbursementBalance = acctReimbursements.stream()
                    .filter(Objects::nonNull)
                    .map(AcctReimbursement::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(reimbursementBalance);
            acctReimbursements = acctReimbursements.stream()
                    .filter(acctReimbursement ->
                            Objects.nonNull(acctReimbursement.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctReimbursement.getShowStatus())
                    ).collect(Collectors.toList());
        }
        List<AcctReimbursement> acctReimbursementsShow = acctReimbursements;

        //待结算账户
        //平安银行账户下“待结算金额”隐藏不展示ZHIFU-8438
        List<AcctSettlement> acctSettlements = uAcctSettlementService.findByCompanyId(companyId);

        List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(businessDebits)) {
            BigDecimal businessDebitBalance = businessDebits.stream().filter(Objects::nonNull).map(AcctBusinessDebit::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(businessDebitBalance);
            businessDebits = businessDebits.stream().filter(acctBusinessDebit -> Objects.nonNull(acctBusinessDebit.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctBusinessDebit.getShowStatus())).collect(Collectors.toList());
        }
        List<AcctBusinessDebit> businessDebitShow = businessDebits;

        List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(individualDebits)) {
            BigDecimal individualDebitBalance = individualDebits.stream().filter(Objects::nonNull).map(AcctIndividualDebit::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(individualDebitBalance);
            individualDebits = individualDebits.stream().filter(acctIndividualDebit -> Objects.nonNull(acctIndividualDebit.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctIndividualDebit.getShowStatus())).collect(Collectors.toList());
        }
        List<AcctIndividualDebit> individualDebitsShow = individualDebits;

        List<AcctBusinessCredit> businessCredits = uAcctBusinessCreditService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(businessCredits)) {
            businessCredits = businessCredits.stream().filter(acctBusinessCredit -> Objects.nonNull(acctBusinessCredit.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctBusinessCredit.getShowStatus())).collect(Collectors.toList());
        }
        List<AcctBusinessCredit> businessCreditsShow = businessCredits;

        List<AcctIndividualCredit> individualCredits = uAcctIndividualCreditService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(individualCredits)) {
            individualCredits = individualCredits.stream().filter(acctIndividualCredit -> Objects.nonNull(acctIndividualCredit.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctIndividualCredit.getShowStatus())).collect(Collectors.toList());
        }
        List<AcctIndividualCredit> individualCreditsShow = individualCredits;

        List<AcctCompanyCard> acctCompanyCards = uAcctCompanyCardService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(acctCompanyCards)) {
            BigDecimal companyCardBalance = acctCompanyCards.stream().filter(Objects::nonNull).map(AcctCompanyCard::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(companyCardBalance);
            acctCompanyCards = acctCompanyCards.stream().filter(acctCompanyCard -> Objects.nonNull(acctCompanyCard.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctCompanyCard.getShowStatus())).collect(Collectors.toList());
        }
        List<AcctCompanyCard> acctCompanyCardsShow = acctCompanyCards;

        List<AcctPublicDetailRespDTO> publicRespDtos = acctPublicSearchService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(publicRespDtos)) {
            BigDecimal publicBalance = publicRespDtos.stream().filter(Objects::nonNull).map(AcctPublicDetailRespDTO::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(publicBalance);
            publicRespDtos = publicRespDtos.stream()
                    .filter(publicDetailRespDTO ->
                            Objects.nonNull(publicDetailRespDTO.getShowStatus()) && FundAcctShowStatusEnum.isShow(publicDetailRespDTO.getShowStatus())
                    ).collect(Collectors.toList());
        }
        List<AcctPublicDetailRespDTO> publicRespDtosShow = publicRespDtos;
        
        List<AcctOversea> acctOverseaList = acctOverseaService.queryByCompanyId(companyId);
        initOverseaAcctBalance(acctOverseaList);

        Integer accountSubType = FundAccountSubType.BUSINESS_ACCOUNT.getKey();
        boolean companySwitch = uCompanySwitchService.isCompanySwitch(companyId);
        //不在白名单 是商务账户 在白名单 在区分平台方
        if (companySwitch) {
            accountSubType = FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey();
        }
        List<FundFreezen> fundFreezens = uFundFreezenService.queryByCompanyIdAndUseTypes(companyId, accountSubType, Arrays.asList(FundAccountModelType.RECHARGE.getKey(), FundAccountModelType.CREDIT.getKey()), FreezenUseType.bankFreezenInt());
        accountGenerals.stream().filter(Objects::nonNull).forEach(general -> {
            //充值主体
            AcctDebitMainRespDTO debitMainRespDTO = AcctMainConvert.buildAcctDebitMainRespDTO(businessDebitShow, individualDebitsShow, acctCompanyCardsShow, publicRespDtosShow, fundFreezens, general,acctSettlements, acctReimbursementsShow, acctOverseaList);
            if (Objects.nonNull(debitMainRespDTO)) {
                acctDebitMainList.add(debitMainRespDTO);
            }
            //授信主体
            buildAcctCreditMainList(acctCreditMainList, businessCreditsShow, individualCreditsShow, general);
        });
        BigDecimal generalBalanc = accountGenerals.stream().filter(Objects::nonNull).map(AccountGeneral::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        allBalance = allBalance.add(generalBalanc);

        List<AcctDebitMainRespDTO> acctDebitMainListSort = acctDebitMainRespSort(acctDebitMainList);
        //红包券
        AccountRedcouponInfoVO redCouponInfo = accountRedcouponManager.queryAccountCouponInfoVO(companyId);
        respDTO.setCompanyId(companyId);
        respDTO.setAllBalance(allBalance);
        respDTO.setAcctDebitMainRespDTOList(acctDebitMainListSort);
        respDTO.setAcctCreditMainRespDTOList(acctCreditMainList);
        respDTO.setAccountRedcouponInfoVO(redCouponInfo);
        return respDTO;
    }

    @Override
    public AcctDebitMainDetailDTO queryAppAcctOverviewDetail(AppOverviewRequestDTO request) {
        String bankAcctNo = request.getBankAcctNo();
        String fxAccountId = request.getFxAccountId();
        if (Objects.isNull(request) || StringUtils.isBlank(request.getCompanyId()) || (StringUtils.isBlank(bankAcctNo) && StringUtils.isBlank(fxAccountId))) {
    		throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
    	}

    	AcctOverviewV5RespDTO v5 = queryWebAcctOverviewV5(new AcctOverviewReqDTO(request.getCompanyId()));
        List<FxCompanyAcctInfos> fxCompanyAcctInfosList = new ArrayList<>();
        if (Objects.isNull(v5) || CollectionUtils.isEmpty(v5.getAcctDebitMainPoolList())) {
    		return null;
    	}

    	List<BankAcctDetailDTO> accts = null;
    	AcctDebitMainPoolRespDTO mp = null;
    	Iterator<AcctDebitMainPoolRespDTO> iter = v5.getAcctDebitMainPoolList().iterator();
    	while (iter.hasNext() && Objects.isNull(accts)) {
            // fxAccountId 是查询海外卡账户信息，包括本主体账户
            Optional<AcctDebitMainRespDTO> companyMainAcct = v5.getAcctDebitMainRespDTOList().stream().filter(v -> Integer.valueOf(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey()).equals(v.getCompanyMainType())).findFirst();
            if (StringUtils.isNotBlank(fxAccountId)) {
                if (companyMainAcct.isPresent()) {
                    bankAcctNo = companyMainAcct.get().getBankAccountNo();
                }
                fxCompanyAcctInfosList = v5.getFxCompanyAcctInfosList();
            } else {
                if (companyMainAcct.isPresent()) {
                    if (companyMainAcct.get().getBankAccountNo().equals(bankAcctNo)) {
                        fxCompanyAcctInfosList = v5.getFxCompanyAcctInfosList();
                    }
                }
            }
            mp = iter.next();
    		if (Objects.isNull(mp) ||
    				(CollectionUtils.isEmpty(mp.getAcctDebitMainRespDTOList()) &&
    						CollectionUtils.isEmpty(mp.getAcctBankEntMainRespDTOList()))) {
    			continue;
    		}

    		if (CollectionUtils.isNotEmpty(mp.getAcctDebitMainRespDTOList())) {
    			AcctDebitMainRespDTO debitTarget = null;
    			Iterator<AcctDebitMainRespDTO> iterator = mp.getAcctDebitMainRespDTOList().iterator();
    			while (iterator.hasNext()) {
    				AcctDebitMainRespDTO tmp = iterator.next();
    				if (Objects.nonNull(tmp) && Objects.equals(tmp.getBankAccountNo(), bankAcctNo)) {
    					debitTarget = tmp;
    					iterator.remove();
    					break;
    				}
    			}
				if (Objects.nonNull(debitTarget)) {
					List<AcctDebitMainRespDTO> newDebits = Lists.newArrayList(debitTarget);
					newDebits.addAll(mp.getAcctDebitMainRespDTOList());
					if (Objects.isNull(accts)) {
						accts = newDebits.stream()
								.map(this :: transform)
								.collect(Collectors.toList());
					}
				}
			}

			if (CollectionUtils.isNotEmpty(mp.getAcctBankEntMainRespDTOList())) {
				if (CollectionUtils.isNotEmpty(accts)) {
					mp.getAcctBankEntMainRespDTOList().stream().map(ent->transformEnt(ent, request.getClientType(), request.getClientVersion())).forEach(accts :: add);
				} else {
					AcctBankEntMainRespDTO entTarget = null;
					Iterator<AcctBankEntMainRespDTO> it = mp.getAcctBankEntMainRespDTOList().iterator();
					while (it.hasNext()) {
						AcctBankEntMainRespDTO ent = it.next();
						if (Objects.nonNull(ent) && Objects.equals(ent.getBankAccountNo(), bankAcctNo)) {
							entTarget = ent;
							it.remove();
							break;
						}
					}
					if (Objects.nonNull(entTarget)) {
						accts = Optional.ofNullable(mp.getAcctDebitMainRespDTOList())
								.orElse(Collections.emptyList())
								.stream().map(this :: transform)
								.collect(Collectors.toList());
						List<BankAcctDetailDTO> newEnts = Lists.newArrayList(transformEnt(entTarget, request.getClientType(), request.getClientVersion()));
						newEnts.addAll(accts);
						List<BankAcctDetailDTO> remainingEnts = Optional.ofNullable(mp.getAcctBankEntMainRespDTOList())
								.orElse(Collections.emptyList())
								.stream()
								.map(ent->transformEnt(ent, request.getClientType(), request.getClientVersion()))
								.collect(Collectors.toList());
						newEnts.addAll(remainingEnts);
						accts = newEnts;
					}
				}
			}
    	}

    	if (CollectionUtils.isEmpty(accts) || Objects.isNull(mp)) {
    		FinhubLogger.warn("根据参数->{}没有找到目标账户", JSON.toJSONString(request));
    		return null;
    	}

    	// 海外卡账户
        if (CollectionUtils.isNotEmpty(fxCompanyAcctInfosList)) {
            for (FxCompanyAcctInfos actInfo : fxCompanyAcctInfosList) {
                BankAcctDetailDTO bankAcctDetailDTO = new BankAcctDetailDTO();
                bankAcctDetailDTO.setFxTotalBalanceShow(actInfo.getCurrencySymbol() + actInfo.getTotalUsdBalanceShow());
                bankAcctDetailDTO.setFxAccountIdMask(actInfo.getAccountIdMask());
                bankAcctDetailDTO.setAccountId(actInfo.getAccountId());
                bankAcctDetailDTO.setFxCompanyAcctInfos(actInfo.getAcctInfos());
                bankAcctDetailDTO.setFxTotalBalance(actInfo.getTotalUsdBalance());
                bankAcctDetailDTO.setBankBackground(FxAcctChannelEnum.AIRWALLEX.getBankBackground());
                bankAcctDetailDTO.setBankIconWebSmall(FxAcctChannelEnum.AIRWALLEX.getBankIconWebSmall());
                bankAcctDetailDTO.setBankIconWebBig(FxAcctChannelEnum.AIRWALLEX.getBankIconWebBig());
                bankAcctDetailDTO.setBankShowName(FxAcctChannelEnum.AIRWALLEX.getChannelName());
                bankAcctDetailDTO.setBankName(FxAcctChannelEnum.AIRWALLEX.getChannel());
                bankAcctDetailDTO.setCompanyId(actInfo.getCompanyId());
                accts.add(bankAcctDetailDTO);
            }
        }

    	return AcctDebitMainDetailDTO.builder()
    			.balance(mp.getAsset())
    			.companyMainName(mp.getShowAccountName())
    			.companyMainType(mp.getCompanyMainType())
    			.accts(accts)
    			.build();
    }

    private BankAcctDetailDTO transformEnt(AcctBankEntMainRespDTO ent, String clientType, String clientVersion) {
    	BankAcctDetailDTO bad = new BankAcctDetailDTO();
		BeanUtils.copyProperties(ent, bad);
		BigDecimal balance = Optional.ofNullable(ent.getAccountBalance()).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
		bad.setBalance(balance);
		bad.setBankShowName(ent.getBankName());
		if ((!StringUtils.isBlank(ent.getBankAccountNo())) && ent.getBankAccountNo().length() > 4) {
			bad.setBankMainShowName(ent.getBankName() + "(" + ent.getBankAccountNo().substring(ent.getBankAccountNo().length() - 4) + ")");
		}
		bad.setBankIconWebSmall(ent.getBankIconWebSmall());
		bad.setBankIconWebBig(ent.getBankIconWebBig());
		bad.setBankBackground(ent.getBankBackground());

        // 客户端新版本取可用余额
        BigDecimal availableBalance = Optional.ofNullable(ent.getAvailableBalance()).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
        if (AppOverviewRequestDTO.CLIENT_TYPE_WEBAPP.equals(clientType) ||
                (Arrays.asList(AppOverviewRequestDTO.CLIENT_TYPE_IOS, AppOverviewRequestDTO.CLIENT_TYPE_ANDROID).contains(clientType) && VersionTool.greaterThan(clientVersion, "5.1.10"))) {
            balance = availableBalance;
        }

        bad.setDirectPayAcct(SimpleDebitAcctDTO.builder()
				.balance(balance)
				.active(ent.isActive())
				.frozenBalance(ent.getFrozenBalance())
				.build());

        if (BigDecimalUtils.hasPrice(ent.getFrozenBalance())) {
            bad.setFrozenDirectPayAcct(SimpleDebitAcctDTO.builder()
                    .balance(ent.getFrozenBalance())
                    .active(ent.isActive())
                    .build());
        }
		bad.setAcctType(3);
		return bad;
    }

    private BankAcctDetailDTO transform(AcctDebitMainRespDTO nd) {
    	BankAcctDetailDTO bad = new BankAcctDetailDTO();
		BeanUtils.copyProperties(nd, bad);

		Optional.ofNullable(nd.getGeneralDetailRespDTO()).ifPresent(biz -> {
			bad.setGeneralAcct(SimpleDebitAcctDTO.builder()
					.accountGeneralId(biz.getAccountGeneralId())
					.accountId(biz.getAccountId())
					.balance(biz.getBalance())
					.active(biz.isActive())
					.build());
			if (BankNameEnum.isFbt(biz.getBankName())) {
				bad.setAcctType(1);
			} else {
				bad.setAcctType(2);
			}
		});

		Optional.ofNullable(nd.getBussDebitDetailRespDTO()).ifPresent(biz ->
			bad.setBizAcct(SimpleDebitAcctDTO.builder()
					.accountGeneralId(biz.getAccountGeneralId())
					.accountId(biz.getAccountId())
					.balance(biz.getBalance())
					.active(biz.isActive())
					.build())
		);
		Optional.ofNullable(nd.getIndlDebitDetailRespDTO()).ifPresent(biz ->
			bad.setBenefitAcct(SimpleDebitAcctDTO.builder()
					.accountGeneralId(biz.getAccountGeneralId())
					.accountId(biz.getAccountId())
					.balance(biz.getBalance())
					.active(biz.isActive())
					.build())
		);
		Optional.ofNullable(nd.getCompanyCardRespDTO()).ifPresent(biz ->
			bad.setCompanyCardAcct(SimpleDebitAcctDTO.builder()
					.accountGeneralId(biz.getAccountGeneralId())
					.accountId(biz.getAccountId())
					.balance(biz.getBalance())
					.active(biz.isActive())
					.build())
		);
		Optional.ofNullable(nd.getAcctReimbursementRespDTO()).ifPresent(biz ->
			bad.setReimbursementAcct(SimpleDebitAcctDTO.builder()
					.accountGeneralId(biz.getAccountGeneralId())
					.accountId(biz.getAccountId())
					.balance(biz.getBalance())
					.active(biz.isActive())
					.build())
		);
		Optional.ofNullable(nd.getPublicDetailRespDTO()).ifPresent(biz ->
			bad.setPublicAcct(SimpleDebitAcctDTO.builder()
					.accountGeneralId(biz.getAccountGeneralId())
					.accountId(biz.getAccountId())
					.balance(biz.getBalance())
					.active(biz.isActive())
					.build())
		);
		Optional.ofNullable(nd.getSettlementDetailRespDTO()).ifPresent(biz -> {
            // 平安银行待结算账户，金额为0是不展示
            if (!(BankNameEnum.isSpa(biz.getBankName()) && Objects.nonNull(biz.getBalance()) && biz.getBalance().compareTo(BigDecimal.ZERO)==0)) {
                bad.setSettlementAcct(SimpleDebitAcctDTO.builder()
                        .accountGeneralId(biz.getAccountGeneralId())
                        .accountId(biz.getAccountId())
                        .balance(biz.getBalance())
                        .active(biz.isActive())
                        .build());
            }
        });
		Optional.ofNullable(nd.getOverseaAcct()).ifPresent(biz -> 
		bad.setOverseaAcct(SimpleDebitAcctDTO.builder()
				.accountGeneralId(biz.getAccountGeneralId())
				.accountId(biz.getAccountId())
				.balance(biz.getBalance())
				.active(biz.isActive())
				.currency(biz.getCurrency())
				.build())
	);
		return bad;
    }

    @Override
    public AppAcctOverviewDTO queryAppAcctOverviewV5(AppOverviewRequestDTO req) {
    	AppAcctOverviewDTO overview = AppAcctOverviewDTO.builder().companyId(req.getCompanyId()).build();
    	if (StringUtils.isBlank(req.getCompanyId())) {
    		return overview;
    	}

    	AcctOverviewV5RespDTO v5 = queryWebAcctOverviewV5(new AcctOverviewReqDTO(req.getCompanyId()));
    	if (Objects.isNull(v5)) {
    		return overview;
    	}

    	if (CollectionUtils.isNotEmpty(v5.getAcctDebitMainPoolList())) {
    		Optional.ofNullable(v5.getAllBalance())
	    		.filter(b -> b.compareTo(BigDecimal.ZERO) >= 0)
	    		.ifPresent(overview :: setAllBalance);
    		BigDecimal generalBalance = calculate(v5.getAcctDebitMainPoolList(), AcctTypeEnum.GENERAL_ACCT);
        	overview.setGeneralBalance(generalBalance);

        	BigDecimal bizBalance = calculate(v5.getAcctDebitMainPoolList(), AcctTypeEnum.BIZ_ACCT);
        	overview.setBizBalance(bizBalance);

        	BigDecimal benefitBalance = calculate(v5.getAcctDebitMainPoolList(), AcctTypeEnum.BENEFIT_ACCT);
        	overview.setBenefitBalance(benefitBalance);

        	BigDecimal companyCardBalance = calculate(v5.getAcctDebitMainPoolList(), AcctTypeEnum.COMPANY_CARD_ACCT);
        	overview.setCompanyCardBalance(companyCardBalance);

        	BigDecimal reimbursementBalance = calculate(v5.getAcctDebitMainPoolList(), AcctTypeEnum.REIMBURSEMENT_ACCT);
        	overview.setReimbursementBalance(reimbursementBalance);

        	BigDecimal publicBalance = calculate(v5.getAcctDebitMainPoolList(), AcctTypeEnum.PUBLIC_ACCT);
        	overview.setPublicBalance(publicBalance);
        	
        	BigDecimal overseaAcctBalance = calculate(v5.getAcctDebitMainPoolList(), AcctTypeEnum.OVERSEA_ACCT);
        	overview.setOverseaBalance(overseaAcctBalance);

        	// 海外卡账户总余额
            if (CollectionUtils.isNotEmpty(v5.getFxCompanyAcctInfosList())) {
                BigDecimal fxTotalBalance = BigDecimal.ZERO;
                for (FxCompanyAcctInfos fxCompanyAcctInfos : v5.getFxCompanyAcctInfosList()) {
                    fxTotalBalance = fxTotalBalance.add(fxCompanyAcctInfos.getTotalUsdBalance());
                }
                overview.setFxAccountBalance(fxTotalBalance);
                overview.setFxCompanyAcctInfosList(v5.getFxCompanyAcctInfosList());
            }
    	}

    	Optional.ofNullable(v5.getAcctBankEntRespDTOList())
    		.filter(CollectionUtils :: isNotEmpty)
    		.ifPresent(entAccts -> {
    			overview.setHaveDirectPayAcct(true);
    			List<AcctBankEntMainRespDTO> abs = entAccts.stream()
    					.filter(Objects :: nonNull)
    					.filter(e -> Objects.nonNull(e.getAccountBalance()))
    					.collect(Collectors.toList());

    			if (CollectionUtils.isNotEmpty(abs)) {
    				BigDecimal directPayBalance = abs.stream()
    						.map(AcctBankEntMainRespDTO :: getAccountBalance)
    						.reduce(BigDecimal.ZERO, BigDecimal :: add);
    				overview.setDirectPayBalance(directPayBalance);
    				overview.setUpdateTime(abs.get(0).getUpdateTime());
    			}
    		});

    	if (req.isWithDetail()) {
    		initDetail4overview(v5, overview);
    	}

        FinhubLogger.info("【账户总览】【queryAppAcctOverviewV5】 respDTO={}" , JsonUtils.toJson(overview));
        
    	return overview;
    }

    private void initDetail4overview(AcctOverviewV5RespDTO v5, AppAcctOverviewDTO overview) {
    	overview.setAssetDesc(v5.getAllAssetDesc());
    	if (CollectionUtils.isNotEmpty(v5.getAcctDebitMainPoolList())) {
    		List<AcctDebitMainDTO> entityAccts = v5.getAcctDebitMainPoolList().stream()
        			.filter(Objects :: nonNull)
        			.map(mainAcct -> {
        				AcctDebitMainDTO dto = AcctDebitMainDTO.builder()
        						.balance(mainAcct.getAsset())
        						.assetDesc(mainAcct.getAssetDesc())
        						.companyMainName(mainAcct.getShowAccountName())
        						.companyMainType(mainAcct.getCompanyMainType())
        						.accts(mainAcct.getAcctDebitMainRespDTOList())
        						.build();
        				Optional.ofNullable(dto.getAccts()).orElse(Collections.emptyList()).forEach(acct -> {
        					if (Objects.nonNull(acct.getGeneralDetailRespDTO())) {
        						if (BankNameEnum.isFbt(acct.getGeneralDetailRespDTO().getBankName())) {
        							acct.setAcctType(1);
        						} else {
        							acct.setAcctType(2);
        						}
        					}
        				});
        				if (CollectionUtils.isNotEmpty(mainAcct.getAcctBankEntMainRespDTOList())) {
        					for (AcctBankEntMainRespDTO ent : mainAcct.getAcctBankEntMainRespDTOList()) {
        						AcctDebitEntRespDTO resp = new AcctDebitEntRespDTO();
        						resp.setBalance(Optional.ofNullable(ent.getAccountBalance()).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE));
        						resp.setCompanyMainType(ent.getCompanyMainType());
        						resp.setBankName(ent.getBankName());
        						resp.setBankAccountNo(ent.getBankAccountNo());
        						resp.setBankMainShowName(ent.getBankShowName());
        						if ((!StringUtils.isBlank(ent.getBankAccountNo())) && ent.getBankAccountNo().length() > 4) {
        							resp.setBankMainShowName(ent.getBankName() + "(" + ent.getBankAccountNo().substring(ent.getBankAccountNo().length() - 4) + ")");
        						}
        						resp.setBankShowName(ent.getBankName());
        						resp.setBankIconWebBig(ent.getBankIconWebBig());
        						resp.setBankIconWebSmall(ent.getBankIconWebSmall());
        						resp.setBankBackground(ent.getBankBackground());
        						resp.setAcctType(3);
        						resp.setUpdateTime(ent.getUpdateTime());
            					if (CollectionUtils.isNotEmpty(dto.getAccts())) {
            						dto.getAccts().add(resp);
            					} else {
            						dto.setAccts(Lists.newArrayList(resp));
            					}
        					}
        				}
        				// 企业外币账户
                        if (CollectionUtils.isNotEmpty(mainAcct.getFxCompanyAcctInfosList())) {
                            dto.setFxCompanyAcctInfosList(mainAcct.getFxCompanyAcctInfosList());
                            BigDecimal fxTotalBalance = BigDecimal.ZERO;
                            for (FxCompanyAcctInfos fxCompanyAcctInfos : mainAcct.getFxCompanyAcctInfosList()) {
                                fxTotalBalance = fxTotalBalance.add(fxCompanyAcctInfos.getTotalUsdBalance());
                            }
                            dto.setFxAccountBalance(fxTotalBalance);
                        }
        				return dto;
        			})
        			.collect(Collectors.toList());
        	overview.setEntityAccts(entityAccts);
    	}

    	if (CollectionUtils.isNotEmpty(v5.getAcctCreditMainRespDTOList())) {
    		List<AcctCreditMainDTO> creditAccts = v5.getAcctCreditMainRespDTOList().stream().filter(Objects :: nonNull)
        			.map(ca -> {
        				AcctCreditMainDTO cm = AcctCreditMainDTO.builder()
        						.companyId(ca.getCompanyId())
        						.companyMainId(ca.getCompanyMainId())
        						.companyMainName(ca.getCompanyMainName())
        						.bankName(ca.getBankName())
        						.bankAccountNo(ca.getBankAccountNo())
        						.balance(ca.getTotalCreditBalance())
        						.initCreditBalance(Optional.ofNullable(ca.getInitCreditBalance()).orElse(ca.getTotalCredit()))
        						.bankMainShowName(ca.getBankMainShowName())
        						.bankIconWebBig(ca.getBankIconWebBig())
        						.bankIconWebSmall(ca.getBankIconWebSmall())
        						.bankBackground(ca.getBankBackground())
        						.build();
        				Optional.ofNullable(ca.getBussCreditDetailRespDTO()).ifPresent(bc ->
        					cm.setBizAcct(SimpleCreditAcct.builder().balance(bc.getBalance()).initCreditBalance(bc.getInitCredit()).build())
        				);
        				Optional.ofNullable(ca.getIndlCreditDetailRespDTO()).ifPresent(bc ->
        					cm.setBenefitAcct(SimpleCreditAcct.builder().balance(bc.getBalance()).initCreditBalance(bc.getInitCredit()).build())
        				);
        				return cm;
        			})
        			.collect(Collectors.toList());
        	overview.setCreditAccts(creditAccts);
    	}

    	overview.setCoupon(v5.getAccountRedcouponInfoVO());
    }

    private BigDecimal calculate(List<AcctDebitMainPoolRespDTO> acctDebitMainPoolList, AcctTypeEnum acctType) {
    	List<BigDecimal> bs = acctDebitMainPoolList
			.stream()
			.map(AcctDebitMainPoolRespDTO :: getAcctDebitMainRespDTOList)
			.filter(CollectionUtils :: isNotEmpty)
			.flatMap(List :: stream)
			.filter(Objects :: nonNull)
			.map(adm -> getAcctBalance(adm, acctType))
			.filter(Objects :: nonNull)
			.filter(b -> b.compareTo(AppAcctOverviewDTO.DEFAULT_BALANCE) > 0)
			.collect(Collectors.toList());

    	if (CollectionUtils.isEmpty(bs)) {
    		return AppAcctOverviewDTO.DEFAULT_BALANCE;
    	}

    	return bs.stream().reduce(BigDecimal.ZERO, BigDecimal :: add);
    }

    private BigDecimal getAcctBalance(AcctDebitMainRespDTO main, AcctTypeEnum acctType) {
    	switch (acctType) {
    	case GENERAL_ACCT:
			return Optional.ofNullable(main.getGeneralDetailRespDTO()).map(AcctGeneralDetailRespDTO :: getBalance).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
		case BIZ_ACCT:
			return Optional.ofNullable(main.getBussDebitDetailRespDTO()).map(AcctDebitDetailRespDTO :: getBalance).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
		case BENEFIT_ACCT:
			return Optional.ofNullable(main.getIndlDebitDetailRespDTO()).map(AcctDebitDetailRespDTO :: getBalance).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
		case COMPANY_CARD_ACCT:
			return Optional.ofNullable(main.getCompanyCardRespDTO()).map(AcctCompanyCardRespDTO :: getBalance).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
		case REIMBURSEMENT_ACCT:
			return Optional.ofNullable(main.getAcctReimbursementRespDTO()).map(AcctReimbursementRespDTO :: getBalance).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
		case PUBLIC_ACCT:
			return Optional.ofNullable(main.getPublicDetailRespDTO()).map(AcctPublicDetailRespDTO :: getBalance).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);
		case OVERSEA_ACCT:
			return Optional.ofNullable(main.getOverseaAcct()).map(AcctOverseaRespDTO :: getBalance).orElse(AppAcctOverviewDTO.DEFAULT_BALANCE);

		default:
			return AppAcctOverviewDTO.DEFAULT_BALANCE;
		}
    }
    
    @Override
    public boolean isGroupTransferAvailable(List<CompanyAccountInfo> authInfo, AvailableGroupCompanyDTO availableGroupCompany) {
    	List<AccountGeneral> generals = getAvailableGeneralAcct4GroupTransfer(authInfo, availableGroupCompany, true);
    	if (CollectionUtils.isEmpty(generals) || generals.size() < 2) {
    		FinhubLogger.warn("【集团资金调拨】用户->{}有权限余额账户数不足->{}", JSON.toJSONString(availableGroupCompany), JSON.toJSONString(authInfo));
    		return false;
    	}
    	
    	return true;
    }
    
    @Override
    public List<AccountGeneral> getAvailableGeneralAcct4GroupTransfer(List<CompanyAccountInfo> authInfo, AvailableGroupCompanyDTO availableGroupCompany, boolean forCheck) {
    	if (StringUtils.isBlank(availableGroupCompany.getGroupId())) {
    		FinhubLogger.warn("【集团资金调拨】待校验groupId为空,参数->{}", JSON.toJSONString(availableGroupCompany));
    		return Collections.emptyList();
    	}
    	
    	GroupTransferWhitelist whitelist = groupTransferWhitelistService.queryByGroupId(availableGroupCompany.getGroupId());
    	if (Objects.isNull(whitelist)) {
    		FinhubLogger.warn("【集团资金调拨】待校验集团不在白名单，参数->{}", JSON.toJSONString(availableGroupCompany));
    		return Collections.emptyList();
    	}
    	
    	if (CollectionUtils.isEmpty(authInfo)) {
    		FinhubLogger.warn("集团资金调拨用户->{}没有权限", JSON.toJSONString(availableGroupCompany));
    		return Collections.emptyList();
    	}
    	
    	List<String> companyIds = authInfo.stream()
    			.filter(ai -> ai.getAcctAuthTypeEnum() != AcctAuthTypeEnum.NO_AUTH || 
    				ai.getAcctAuthEnum() != AcctAuthEnum.NO_AUTH)
    			.map(CompanyAccountInfo :: getCompanyId)
    			.filter(org.apache.commons.lang3.StringUtils :: isNotBlank)
    			.distinct()
    			.collect(Collectors.toList());
    	if (CollectionUtils.isEmpty(companyIds) || companyIds.size() < 2) {
    		FinhubLogger.warn("集团资金调拨用户->{}有权限公司数不足", JSON.toJSONString(availableGroupCompany));
    		return Collections.emptyList();
    	}
    			
    	List<AccountGeneral> gs = uAcctGeneralService.findByCompanyIdAndBankNames(companyIds, BankNameEnum.FBT.getCode());
    	if (!forCheck) {
    		return gs;
    	}
    	if (CollectionUtils.isEmpty(gs) || gs.size() < 2) {
    		FinhubLogger.warn("集团资金调拨用户->{}有权限余额账户数不足", JSON.toJSONString(availableGroupCompany));
    		return Collections.emptyList();
    	}
    	
    	List<AcctBusinessDebit> bizAccts = acctBusinessDebitService.findByCompanyIdsAndBankName(companyIds, BankNameEnum.FBT.getCode());
    	if (CollectionUtils.isEmpty(bizAccts)) {
    		FinhubLogger.warn("集团资金调拨用户->{}没有商务消费账户", JSON.toJSONString(availableGroupCompany));
    		return Collections.emptyList();
    	}
    	
    	Set<String> generalIds = bizAccts.stream()
    			.filter(acct -> Objects.equals(1, acct.getActiveStatus()))
    			.map(AcctBusinessDebit :: getAccountGeneralId)
    			.distinct()
    			.collect(Collectors.toSet());
    	if (CollectionUtils.isEmpty(bizAccts)) {
    		FinhubLogger.warn("集团资金调拨用户->{}没有生效的商务消费账户", JSON.toJSONString(availableGroupCompany));
    		return Collections.emptyList();
    	}
    	
    	
    	List<AccountGeneral> result = gs.stream()
    			.filter(ge -> generalIds.contains(ge.getAccountGeneralId()))
    			.collect(Collectors.toList());
    	
    	if (CollectionUtils.isEmpty(result) || result.size() < 2) {
    		FinhubLogger.warn("【集团资金调拨】用户->{}有权限余额账户数不足->{}", JSON.toJSONString(availableGroupCompany), JSON.toJSONString(authInfo));
    		return Collections.emptyList();
    	}
    	
    	boolean matched = gs.stream()
    			.filter(Objects :: nonNull)
    			.map(AccountGeneral :: getBalance)
    			.filter(Objects :: nonNull)
    			.anyMatch(balance -> balance.compareTo(BigDecimal.ZERO) > 0);
    	if (matched) {
    		return result;
    	}
    	
    	matched = bizAccts.stream()
    			.filter(Objects :: nonNull)
    			.map(AcctBusinessDebit :: getBalance)
    			.filter(Objects :: nonNull)
    			.anyMatch(balance -> balance.compareTo(BigDecimal.ZERO) > 0);
    	if (matched) {
    		return result;
    	}
    	
    	List<AcctIndividualDebit> aids = acctIndividualDebitService.findCompanyIdAndBankName(companyIds, BankNameEnum.FBT.getCode());
    	matched = Optional.ofNullable(aids).orElse(Collections.emptyList())
    			.stream()
    			.filter(Objects :: nonNull)
    			.map(AcctIndividualDebit :: getBalance)
    			.filter(Objects :: nonNull)
    			.anyMatch(balance -> balance.compareTo(BigDecimal.ZERO) > 0);
    	if (matched) {
    		return result;
    	}
    	
    	List<AcctCompanyCard> cards = acctCompanyCardService.findByCompanyIdAndBankName(companyIds, BankNameEnum.FBT.getCode());
    	matched = Optional.ofNullable(cards).orElse(Collections.emptyList())
    			.stream()
    			.filter(Objects :: nonNull)
    			.map(AcctCompanyCard :: getBalance)
    			.filter(Objects :: nonNull)
    			.anyMatch(balance -> balance.compareTo(BigDecimal.ZERO) > 0);
    	if (matched) {
    		return result;
    	}
    	
    	return Collections.emptyList();
    }

    @Override
    public AcctOverviewV5RespDTO queryWebAcctOverviewV5(AcctOverviewReqDTO reqDTO) {
        String companyId = reqDTO.getCompanyId();
        AcctOverviewV5RespDTO respDTO = new AcctOverviewV5RespDTO(companyId);
        if(StringUtils.isBlank(companyId)){
            return respDTO;
        }
        // 查询网关信息
        AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctComGwByComIdReqDTO.setCompanyId(companyId);
        AcctComGwRespDTO acctComGwRespDTO = acctCompanyGatewayService.findActGwsByComId(acctComGwByComIdReqDTO);
        FinhubLogger.info("queryWebAcctOverviewV5  req :{} res = {}",JsonUtils.toJson(acctComGwByComIdReqDTO) ,JsonUtils.toJson(acctComGwRespDTO));
        if(Objects.isNull(acctComGwRespDTO) || CollectionUtils.isEmpty(acctComGwRespDTO.getAcctComGwAcctRespDTOS())){
            FinhubLogger.error("【账户总览】【queryWebAcctOverview】未查询到网关信息 companyId={}" , companyId);
            return respDTO;
        }
        AcctOverviewRespDTO commonQueryWebAcctOverview = commonQueryWebAcctOverview(reqDTO);
        FinhubLogger.info("queryWebAcctOverviewV5  commonQueryWebAcctOverview req :{} res = {}",JsonUtils.toJson(reqDTO),JsonUtils.toJson(commonQueryWebAcctOverview));
        BeanUtils.copyProperties(commonQueryWebAcctOverview, respDTO);
        // 外币账户额度处理
        FinhubLogger.info("queryWebAcctOverviewV5  fxCompanyAcctBuild req :{}",companyId);
        List<FxCompanyAcctInfos> fxCompanyAcctInfos = fxCompanyAcctBuild(companyId);
        respDTO.setFxCompanyAcctInfosList(fxCompanyAcctInfos);

        FinhubLogger.info("【账户总览】【queryWebAcctOverview】 respDTO={}" , JsonUtils.toJson(respDTO));
        respDTO.createAccountStructureUrl(acctComGwRespDTO.getAcctComGwAcctRespDTOS().get(0).getCompanyModel());
        respDTO.createAcctDebitMainPool();
        respDTO.createAllBalance();

        if (CollectionUtils.isNotEmpty(fxCompanyAcctInfos)) {
            BigDecimal fxAllUsdAmount = BigDecimal.ZERO;
            for (FxCompanyAcctInfos fxCompanyAcctInfo : fxCompanyAcctInfos) {
                if (Objects.nonNull(fxCompanyAcctInfo.getTotalUsdBalance())) {
                    fxAllUsdAmount = fxAllUsdAmount.add(fxCompanyAcctInfo.getTotalUsdBalance());
                }
            }
            respDTO.setAllUsdBalance(fxAllUsdAmount);
            respDTO.setAllUsdBalanceShow(MoneyUtils.formatYuan(MoneyUtils.fen2yuan(fxAllUsdAmount), true, false));
        }

        respDTO.createAllAssetsDesc();
        FinhubLogger.info("【账户总览】【queryWebAcctOverviewV5】 respDTO={}" , JsonUtils.toJson(respDTO));

        return respDTO;
    }

    @Override
    public AcctGroupOverviewV5RespDTO queryWebGroupAcctOverviewV5(List<CompanyAccountInfo> companyAccountInfos) {
        AcctGroupOverviewV5RespDTO respDTO = new AcctGroupOverviewV5RespDTO();
        AcctDebitGroupDetailRespDTO debitGroupDetail = new AcctDebitGroupDetailRespDTO();
        AcctCreditGroupDetailRespDTO creditGroupDetail = new AcctCreditGroupDetailRespDTO();
        AcctRedCouponGroupDetailRespDTO redCouponGroupDetail = new AcctRedCouponGroupDetailRespDTO();

        BigDecimal totalDebitBalance = new BigDecimal(0);
        BigDecimal totalFxBalance = null;
        //是否展示集团总资金，如果用户拥有该集团下面所有企业账户的账户权限，才会展示
        boolean isShowGroupTotalDebitBalance = Boolean.TRUE;
        //集团下各企业账户的账户数据列表
        List<AcctDebitCompanyDetailRespDTO> acctDebitCompanyRespDTOS = new ArrayList<>();

        BigDecimal totalCredit = new BigDecimal(0);
        BigDecimal totalCreditBalance = new BigDecimal(0);
        //是否展示集团总授信额度，如果用户拥有该集团下面所有企业账户的账户权限，才会展示
        boolean isShowGroupTotalCreditBalance = Boolean.TRUE;
        List<AcctCreditMainRespDTO> acctCreditMainRespDTOS = new ArrayList<>();

        BigDecimal totalRedCouponBalance = new BigDecimal(0);
        //是否展示集团红包券总余额，如果用户拥有该集团下面所有企业账户的账户权限，才会展示
        boolean isShowGroupTotalRedCouponBalance = Boolean.TRUE;
        List<AccountRedcouponInfoVO> acctRedCouponCompanyList = new ArrayList<>();

        companyAccountInfos = companyAccountInfos.stream().sorted(Comparator.comparing(CompanyAccountInfo::getCompanyType)).collect(Collectors.toList());
        //遍历集团下各企业列表，单个企业去查账户信息，并过滤掉用户没有权限查看的账户，后汇总
        for (CompanyAccountInfo comAuthInfo : companyAccountInfos) {
            CompanyAccountInfo.AcctAuthEnum acctAuthEnum = comAuthInfo.getAcctAuthEnum();
            if (CompanyAccountInfo.AcctAuthEnum.NO_AUTH.equals(acctAuthEnum)) {
                isShowGroupTotalDebitBalance = Boolean.FALSE;
                isShowGroupTotalCreditBalance = Boolean.FALSE;
                isShowGroupTotalRedCouponBalance = Boolean.FALSE;
                continue;
            }

            AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO();
            reqDTO.setCompanyId(comAuthInfo.getCompanyId());
            //查询该企业账户信息
            AcctOverviewV5RespDTO acctOverviewV5RespDTO = queryWebAcctOverviewV5(reqDTO);

            //用户在该企业有权限的账户集合
            List<BaseAccountVO> authAccounts = comAuthInfo.getPermissionAccounts();
            Map<String, List<BaseAccountVO>> authAccountMap = authAccounts.stream().collect(Collectors.groupingBy(BaseAccountVO::getAccountId));
            Map<String, List<BaseAccountVO>> fxAuthAccountMap = comAuthInfo.getFxPermissionAccounts().stream().collect(Collectors.groupingBy(BaseAccountVO::getAccountId));

            //构建该企业账号充值账户数据
            AcctDebitCompanyDetailRespDTO acctDebitCompanyDetailRespDTO = buildCompanyDebitData(acctOverviewV5RespDTO.getAcctDebitMainPoolList(), comAuthInfo, authAccountMap, fxAuthAccountMap);
            if (acctDebitCompanyDetailRespDTO != null && CollectionUtils.isNotEmpty(acctDebitCompanyDetailRespDTO.getAcctDebitMainPoolList())) {
                acctDebitCompanyRespDTOS.add(acctDebitCompanyDetailRespDTO);
                BigDecimal companyDebitTotalBalance = acctDebitCompanyDetailRespDTO.getBalance();
                isShowGroupTotalDebitBalance = companyDebitTotalBalance == null ? Boolean.FALSE : isShowGroupTotalDebitBalance;
                if (isShowGroupTotalDebitBalance) {
                    totalDebitBalance = totalDebitBalance.add(companyDebitTotalBalance);
                    if (acctDebitCompanyDetailRespDTO.getFxBalance() != null) {
                        totalFxBalance = totalFxBalance == null ? BigDecimal.ZERO: totalFxBalance;
                        totalFxBalance = totalFxBalance.add(acctDebitCompanyDetailRespDTO.getFxBalance());
                    }
                }
            }

            //构建该企业账号授信账户数据
            AcctCreditMainRespDTO creditMainRespDTO = buildCompanyCreditData(acctOverviewV5RespDTO.getAcctCreditMainRespDTOList(), comAuthInfo, authAccountMap);
            if (creditMainRespDTO != null) {
                BigDecimal creditBalance = creditMainRespDTO.getBalance();
                if (creditBalance == null) {
                    isShowGroupTotalCreditBalance = Boolean.FALSE;
                } else {
                    acctCreditMainRespDTOS.add(creditMainRespDTO);
                    totalCredit = totalCredit.add(creditMainRespDTO.getTotalCredit());
                    totalCreditBalance = totalCreditBalance.add(creditMainRespDTO.getTotalCreditBalance());
                }
            }

            //构建该企业账号红包券账户数据
            AccountRedcouponInfoVO accountRedcouponInfoVO = buildCompanyRedcouponData(acctOverviewV5RespDTO.getAccountRedcouponInfoVO(), comAuthInfo, authAccountMap);
            if (accountRedcouponInfoVO != null) {
                BigDecimal redCouponBalance = accountRedcouponInfoVO.getBalance();
                if (redCouponBalance == null) {
                    isShowGroupTotalRedCouponBalance = Boolean.FALSE;
                } else {
                    acctRedCouponCompanyList.add(accountRedcouponInfoVO);
                    totalRedCouponBalance = totalRedCouponBalance.add(accountRedcouponInfoVO.getBalance());
                }
            }
        }

        if (isShowGroupTotalDebitBalance) {
            debitGroupDetail.setTotalDebitBalance(totalDebitBalance);
            debitGroupDetail.setFxTotalDebitBalance(totalFxBalance);
        }
        debitGroupDetail.setAcctDebitGroupList(acctDebitCompanyRespDTOS);
        respDTO.setGroupDebitAcctData(debitGroupDetail);

        if (isShowGroupTotalCreditBalance) {
            creditGroupDetail.setTotalGroupCredit(totalCredit);
            creditGroupDetail.setTotalGroupCreditBalance(totalCreditBalance);
        }
        creditGroupDetail.setCreditCompanyAcctList(acctCreditMainRespDTOS);
        respDTO.setGroupCreditAcctData(creditGroupDetail);

        if (isShowGroupTotalRedCouponBalance) {
            redCouponGroupDetail.setTotalBalance(totalRedCouponBalance);
        }
        if (CollectionUtils.isNotEmpty(acctRedCouponCompanyList)) {
            redCouponGroupDetail.setRedcouponInstructions(acctRedCouponCompanyList.get(0).getRedcouponInstructions());
        }
        redCouponGroupDetail.setCompanyRedcouponAcctList(acctRedCouponCompanyList);
        respDTO.setGroupRedcouponData(redCouponGroupDetail);

        return respDTO;
    }

    private AcctDebitCompanyDetailRespDTO buildCompanyDebitData(List<AcctDebitMainPoolRespDTO> acctDebitMainPoolList, CompanyAccountInfo comAuthInfo, Map<String, List<BaseAccountVO>> authAccountMap, Map<String, List<BaseAccountVO>> fxAuthAccountMap) {
        if (CollectionUtils.isEmpty(acctDebitMainPoolList)) {
            return null;
        }

        Iterator<AcctDebitMainPoolRespDTO> debitMainPoolIterator = acctDebitMainPoolList.iterator();
        //是否展示企业账号总金额
        boolean isShowCompanyTotalDebitBalance = Boolean.TRUE;
        AcctDebitCompanyDetailRespDTO companyDebitAcctDetail = new AcctDebitCompanyDetailRespDTO();
        BigDecimal companyDebitTotalBalance = new BigDecimal(0);
        BigDecimal fxTotalBalance = null;
        while (debitMainPoolIterator.hasNext()) {
            AcctDebitMainPoolRespDTO debitMainPoolRespDTO = debitMainPoolIterator.next();
            companyDebitTotalBalance = companyDebitTotalBalance.add(debitMainPoolRespDTO.getAsset());
            boolean isShowMainTotalBalance = Boolean.TRUE;
            //充值账户
            List<AcctDebitMainRespDTO> acctDebitDetailRespDTOList = debitMainPoolRespDTO.getAcctDebitMainRespDTOList();
            if (CollectionUtils.isNotEmpty(acctDebitDetailRespDTOList)) {
                Iterator<AcctDebitMainRespDTO> debitMainRespDTOIterator = acctDebitDetailRespDTOList.iterator();
                while (debitMainRespDTOIterator.hasNext()) {
                    AcctDebitMainRespDTO acctDebitDetail = debitMainRespDTOIterator.next();
                    if (CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA.equals(comAuthInfo.getAcctAuthEnum())) {
                        continue;
                    }
                    List<BaseAccountVO> authAccountInfoVOS = authAccountMap.get(acctDebitDetail.getAccountGeneralId());
                    if (CollectionUtils.isEmpty(authAccountInfoVOS)) {
                        isShowMainTotalBalance = Boolean.FALSE;
                        debitMainRespDTOIterator.remove();
                        continue;
                    }
                    if (BankNameEnum.FBT.getCode().equals(acctDebitDetail.getBankName())) {
                        List<BaseAccountVO> rechargeAccountInfos = authAccountInfoVOS.stream()
                                .filter(accountInfoVO -> FundAccountModelType.isRecharge(accountInfoVO.getAccountModel())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(rechargeAccountInfos)) {
                            isShowMainTotalBalance = Boolean.FALSE;
                            debitMainRespDTOIterator.remove();
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(acctDebitDetailRespDTOList)) {
                //分贝通账户排序在前面
                debitMainPoolRespDTO.setAcctDebitMainRespDTOList(acctDebitDetailRespDTOList.stream().sorted((m1, m2) -> {
                    if (BankNameEnum.isFbt(m1.getBankName())) {
                        return -1;
                    } else if (BankNameEnum.isFbt(m2.getBankName())) {
                        return 1;
                    } else {
                        return 0;
                    }
                }).collect(Collectors.toList()));
            }

            //银企直连账户
            List<AcctBankEntMainRespDTO> acctBankEntMainRespDTOList = debitMainPoolRespDTO.getAcctBankEntMainRespDTOList();
            if (CollectionUtils.isNotEmpty(acctBankEntMainRespDTOList)) {
                Iterator<AcctBankEntMainRespDTO> bankEntMainIterator = acctBankEntMainRespDTOList.iterator();
                while (bankEntMainIterator.hasNext()) {
                    AcctBankEntMainRespDTO bankEntMainRespDTO = bankEntMainIterator.next();
                    if (CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA.equals(comAuthInfo.getAcctAuthEnum())) {
                        continue;
                    }
                    List<BaseAccountVO> authAccountInfoVOS = authAccountMap.get(bankEntMainRespDTO.getAcctInfoId());
                    if (CollectionUtils.isEmpty(authAccountInfoVOS)) {
                        isShowMainTotalBalance = Boolean.FALSE;
                        bankEntMainIterator.remove();
                    }
                }
            }

            // 海外卡账户
            List<FxCompanyAcctInfos> fxAcctInfos = debitMainPoolRespDTO.getFxCompanyAcctInfosList();
            if (CollectionUtils.isNotEmpty(fxAcctInfos)) {
                Iterator<FxCompanyAcctInfos> fxIterator = fxAcctInfos.iterator();
                while (fxIterator.hasNext()) {
                    FxCompanyAcctInfos fxAccount = fxIterator.next();
                    fxTotalBalance = fxTotalBalance == null ? BigDecimal.ZERO : fxTotalBalance;
                    fxTotalBalance = fxTotalBalance.add(fxAccount.getTotalUsdBalance());
                    if (CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA.equals(comAuthInfo.getAcctAuthEnum())) {
                        continue;
                    }
                    List<BaseAccountVO> baseAccountVOS = fxAuthAccountMap.get(fxAccount.getAccountId());
                    if (CollectionUtils.isEmpty(baseAccountVOS)) {
                        isShowMainTotalBalance = Boolean.FALSE;
                        fxIterator.remove();
                        if (Integer.valueOf(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey()).equals(debitMainPoolRespDTO.getCompanyMainType())) {
                            debitMainPoolRespDTO.setFxBalance(null);
                            String assetDesc = debitMainPoolRespDTO.getAssetDesc();
                            if (!StringUtils.isBlank(assetDesc)) {
                                if ("外币账户资金总和".equals(assetDesc)) {
                                    debitMainPoolRespDTO.setAssetDesc(null);
                                } else {
                                    debitMainPoolRespDTO.setAssetDesc(assetDesc.replace("+外币账户资金总和", "总和"));
                                }
                            }
                        }
                    }
                }
            }

            if (!isShowMainTotalBalance) {
                isShowCompanyTotalDebitBalance = Boolean.FALSE;
                debitMainPoolRespDTO.setAsset(null);
            }
            if (CollectionUtils.isEmpty(acctDebitDetailRespDTOList) && CollectionUtils.isEmpty(acctBankEntMainRespDTOList) && CollectionUtils.isEmpty(fxAcctInfos)) {
                debitMainPoolIterator.remove();
            }
        }
        companyDebitAcctDetail.setBalance(isShowCompanyTotalDebitBalance ? companyDebitTotalBalance : null);
        companyDebitAcctDetail.setFxBalance(isShowCompanyTotalDebitBalance ? fxTotalBalance : null);
        companyDebitAcctDetail.setCompanyGroupType(comAuthInfo.getCompanyType());
        companyDebitAcctDetail.setCompanyAcctShowName(comAuthInfo.getCompanyName());

        companyDebitAcctDetail.setAcctDebitMainPoolList(acctDebitMainPoolList.stream().sorted(Comparator.comparing(AcctDebitMainPoolRespDTO::getCompanyMainType)).collect(Collectors.toList()));
        return companyDebitAcctDetail;
    }

    private AcctCreditMainRespDTO buildCompanyCreditData(List<AcctCreditMainRespDTO> acctCreditMainRespDTOList, CompanyAccountInfo comAuthInfo, Map<String, List<BaseAccountVO>> authAccountMap) {
        if (CollectionUtils.isEmpty(acctCreditMainRespDTOList)) {
            return null;
        }

        AcctCreditMainRespDTO creditMainRespDTO = acctCreditMainRespDTOList.get(0);
        if (creditMainRespDTO != null) {
            creditMainRespDTO.setCompanyGroupType(comAuthInfo.getCompanyType());
            if (CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA.equals(comAuthInfo.getAcctAuthEnum())) {
                return creditMainRespDTO;
            }
            List<BaseAccountVO> authAccountInfoVOS = authAccountMap.get(creditMainRespDTO.getAccountGeneralId());
            if (CollectionUtils.isNotEmpty(authAccountInfoVOS)) {
                List<BaseAccountVO> creditAccountInfos = authAccountInfoVOS.stream()
                        .filter(accountInfoVO -> FundAccountModelType.isCredit(accountInfoVO.getAccountModel())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(creditAccountInfos)) {
                    return creditMainRespDTO;
                }
            }
            creditMainRespDTO.setBalance(null);
        }
        return creditMainRespDTO;
    }

    private AccountRedcouponInfoVO buildCompanyRedcouponData(AccountRedcouponInfoVO acctRedcouponInfoVO, CompanyAccountInfo comAuthInfo, Map<String, List<BaseAccountVO>> authAccountMap) {
        if (acctRedcouponInfoVO == null) {
            return null;
        }

        acctRedcouponInfoVO.setCompanyGroupType(comAuthInfo.getCompanyType());
        acctRedcouponInfoVO.setCompanyName(comAuthInfo.getCompanyName());
        if (CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA.equals(comAuthInfo.getAcctAuthEnum())) {
            return acctRedcouponInfoVO;
        }
        List<BaseAccountVO> authAccountInfoVOS = authAccountMap.get(acctRedcouponInfoVO.getAccountRedcouponId());
        if (CollectionUtils.isNotEmpty(authAccountInfoVOS)) {
            return acctRedcouponInfoVO;
        }
        acctRedcouponInfoVO.setBalance(null);
        return acctRedcouponInfoVO;
    }

    @Override
    public List<AcctOverviewV5ListRespDTO> queryWebAcctList(AcctOverviewReqDTO reqDTO) {
        String companyId = reqDTO.getCompanyId();
        AcctOverviewV5RespDTO respDTO = new AcctOverviewV5RespDTO(companyId);
        if(StringUtils.isBlank(companyId)){
            return null;
        }
        AcctOverviewRespDTO commonQueryWebAcctOverview = commonQueryWebAcctOverview(reqDTO);
        BeanUtils.copyProperties(commonQueryWebAcctOverview, respDTO);
        respDTO.createAcctDebitMainPool();
        List<AcctOverviewV5ListRespDTO> respListDTO = new ArrayList<>();
        List<AcctDebitMainPoolRespDTO> acctDebitMainPoolList = respDTO.getAcctDebitMainPoolList();
        if(CollectionUtils.isNotEmpty(acctDebitMainPoolList)){
            for (AcctDebitMainPoolRespDTO mainPoolRespDTO : acctDebitMainPoolList) {
                if(CollectionUtils.isNotEmpty(mainPoolRespDTO.getAcctDebitMainRespDTOList())){
                    for (AcctDebitMainRespDTO acctDebitMainRespDTO : mainPoolRespDTO.getAcctDebitMainRespDTOList()) {
                        AcctOverviewV5ListRespDTO acctOverviewV5ListRespDTO = AcctOverviewV5Convert.acctDebitMainToAcctOverviewV5(acctDebitMainRespDTO);
                        respListDTO.add(acctOverviewV5ListRespDTO);
                    }
                }
            }
        }
        //银企直联
        List<AcctBankEntMainRespDTO> acctBankEntMainRespDTOList =respDTO.getAcctBankEntRespDTOList();
        if (CollectionUtils.isNotEmpty(acctBankEntMainRespDTOList)){
            for (AcctBankEntMainRespDTO acctBankEntMainRespDTO:acctBankEntMainRespDTOList) {
                AcctOverviewV5ListRespDTO acctOverviewV5ListRespDTO = AcctOverviewV5Convert.acctBankEntMainToAcctOverviewV5(acctBankEntMainRespDTO);
                respListDTO.add(acctOverviewV5ListRespDTO);
            }
        }
        return respListDTO;
    }
    
    @Override
    public AcctDebitMainPoolRespDTO queryFBTAcctFromGroup(List<CompanyAccountInfo> companyAccountInfos) {
    	AcctDebitMainPoolRespDTO resp = new AcctDebitMainPoolRespDTO();
    	if (CollectionUtils.isEmpty(companyAccountInfos)) {
    		return resp;
    	}
    	AcctGroupOverviewV5RespDTO overview = queryWebGroupAcctOverviewV5(companyAccountInfos);
    	if (CollectionUtils.isEmpty(overview.getGroupDebitAcctData().getAcctDebitGroupList())) {
    		return resp;
    	}
    	
    	resp.setAsset(overview.getGroupDebitAcctData().getTotalDebitBalance());
    	List<AcctDebitMainRespDTO> result = Lists.newArrayList();
    	resp.setAcctDebitMainRespDTOList(result);
    	for (AcctDebitCompanyDetailRespDTO respDTO : overview.getGroupDebitAcctData().getAcctDebitGroupList()) {
    		if (CollectionUtils.isEmpty(respDTO.getAcctDebitMainPoolList())) {
    			continue;
    		}
    		for (AcctDebitMainPoolRespDTO mainPoolResp : respDTO.getAcctDebitMainPoolList()) {
    			if (CollectionUtils.isEmpty(mainPoolResp.getAcctDebitMainRespDTOList())) {
    				continue;
    			}
    			for (AcctDebitMainRespDTO acctDebitMain : mainPoolResp.getAcctDebitMainRespDTOList()) {
    				if (!Objects.equals(BankNameEnum.FBT.getCode(), acctDebitMain.getBankName())) {
    					continue;
    				}
    				Optional<BigDecimal> optional = Optional.ofNullable(acctDebitMain.getGeneralDetailRespDTO())
    						.map(AcctGeneralDetailRespDTO :: getBalance)
    						.filter(Objects :: nonNull);
    				
    				acctDebitMain.setTotalBalance(respDTO.getBalance());
					acctDebitMain.setGeneralBalance(optional.orElse(BigDecimal.ZERO));
					result.add(acctDebitMain);
    			}
    		}
    	}
    	return resp;
    }

    @Override
    public List<AcctOverviewV5ListRespDTO> queryWebGroupAcctList(List<CompanyAccountInfo> companyAccountInfos) {
        AcctGroupOverviewV5RespDTO respDTO = queryWebGroupAcctOverviewV5(companyAccountInfos);

        List<AcctOverviewV5ListRespDTO> respListDTO = new ArrayList<>();
        AcctDebitGroupDetailRespDTO groupDebitAcctData = respDTO.getGroupDebitAcctData();
        List<AcctDebitCompanyDetailRespDTO> acctDebitGroupList = groupDebitAcctData.getAcctDebitGroupList();
        if (CollectionUtils.isNotEmpty(acctDebitGroupList)) {
            for (AcctDebitCompanyDetailRespDTO acctDebitCompanyDetail : acctDebitGroupList) {
                List<AcctDebitMainPoolRespDTO> acctDebitMainPoolList = acctDebitCompanyDetail.getAcctDebitMainPoolList();
                if(CollectionUtils.isNotEmpty(acctDebitMainPoolList)){
                    for (AcctDebitMainPoolRespDTO mainPoolRespDTO : acctDebitMainPoolList) {
                        if (CollectionUtils.isNotEmpty(mainPoolRespDTO.getAcctDebitMainRespDTOList())) {
                            for (AcctDebitMainRespDTO acctDebitMainRespDTO : mainPoolRespDTO.getAcctDebitMainRespDTOList()) {
                                AcctOverviewV5ListRespDTO acctOverviewV5ListRespDTO = AcctOverviewV5Convert.acctDebitMainToAcctOverviewV5(acctDebitMainRespDTO);
                                respListDTO.add(acctOverviewV5ListRespDTO);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(mainPoolRespDTO.getAcctBankEntMainRespDTOList())){
                            for (AcctBankEntMainRespDTO acctBankEntMainRespDTO : mainPoolRespDTO.getAcctBankEntMainRespDTOList()) {
                                AcctOverviewV5ListRespDTO acctOverviewV5ListRespDTO = AcctOverviewV5Convert.acctBankEntMainToAcctOverviewV5(acctBankEntMainRespDTO);
                                respListDTO.add(acctOverviewV5ListRespDTO);
                            }
                        }

                        // 海外卡账户
                        if (CollectionUtils.isNotEmpty(mainPoolRespDTO.getFxCompanyAcctInfosList())){
                            for (FxCompanyAcctInfos fxCompanyAcctInfo : mainPoolRespDTO.getFxCompanyAcctInfosList()) {
                                AcctOverviewV5ListRespDTO acctOverviewV5ListRespDTO = AcctOverviewV5Convert.fxAcctToAcctOverviewV5(fxCompanyAcctInfo);
                                respListDTO.add(acctOverviewV5ListRespDTO);
                            }
                        }
                    }
                }
            }
        }
        return respListDTO;
    }

    /**
     * 账户总览通用查询方法
     * @param reqDTO AcctOverviewReqDTO
     * @return AcctOverviewRespDTO
     */
    private AcctOverviewRespDTO commonQueryWebAcctOverview(AcctOverviewReqDTO reqDTO) {
        String companyId = reqDTO.getCompanyId();
        AcctOverviewRespDTO respDTO = new AcctOverviewRespDTO(companyId);
        if(StringUtils.isBlank(companyId)){
            return respDTO;
        }
        List<AcctDebitMainRespDTO> acctDebitMainList = new ArrayList<>();
        List<AcctCreditMainRespDTO> acctCreditMainList = new ArrayList<>();
        
        CompletableFuture<List<AcctBankEntMainRespDTO>> future = CompletableFuture.supplyAsync(() -> addProperties4BankEnt(companyId), asyncExecutor);

        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return respDTO;
        }

        BigDecimal allBalance = BigDecimal.ZERO;

        //员工报销账户
        List<AcctReimbursement> acctReimbursements = uAcctReimbursementService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(acctReimbursements)) {
            BigDecimal reimbursementBalance = acctReimbursements.stream()
                    .filter(Objects::nonNull)
                    .map(AcctReimbursement::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(reimbursementBalance);
        }
        List<AcctReimbursement> acctReimbursementsShow = acctReimbursements;

        //平安银行账户下“待结算金额”隐藏不展示ZHIFU-8438
        List<AcctSettlement> acctSettlements = uAcctSettlementService.findByCompanyId(companyId);

        List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(businessDebits)) {
            BigDecimal businessDebitBalance = businessDebits.stream()
                    .filter(Objects::nonNull)
                    .map(AcctBusinessDebit::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(businessDebitBalance);
        }
        List<AcctBusinessDebit> businessDebitShow = businessDebits;

        List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(individualDebits)) {
            BigDecimal individualDebitBalance = individualDebits.stream()
                    .filter(Objects::nonNull)
                    .map(AcctIndividualDebit::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(individualDebitBalance);
        }
        List<AcctIndividualDebit> individualDebitsShow = individualDebits;

        List<AcctBusinessCredit> businessCredits = uAcctBusinessCreditService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(businessCredits)) {
            businessCredits = businessCredits.stream()
                    .filter(acctBusinessCredit -> Objects.nonNull(acctBusinessCredit.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctBusinessCredit.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctBusinessCredit> businessCreditsShow = businessCredits;

        List<AcctIndividualCredit> individualCredits = uAcctIndividualCreditService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(individualCredits)) {
            individualCredits = individualCredits.stream()
                    .filter(acctIndividualCredit -> Objects.nonNull(acctIndividualCredit.getShowStatus()) && FundAcctShowStatusEnum.isShow(acctIndividualCredit.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctIndividualCredit> individualCreditsShow = individualCredits;

        List<AcctCompanyCard> acctCompanyCards = uAcctCompanyCardService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(acctCompanyCards)) {
            BigDecimal companyCardBalance = acctCompanyCards.stream()
                    .filter(Objects::nonNull)
                    .map(AcctCompanyCard::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(companyCardBalance);
        }
        List<AcctCompanyCard> acctCompanyCardsShow = acctCompanyCards;

        List<AcctPublicDetailRespDTO> publicRespDtos = acctPublicSearchService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(publicRespDtos)) {
            BigDecimal publicBalance = publicRespDtos.stream()
                    .filter(Objects::nonNull).map(AcctPublicDetailRespDTO::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            allBalance = allBalance.add(publicBalance);
        }
        List<AcctPublicDetailRespDTO> publicRespDtosShow = publicRespDtos;
        
        List<AcctOversea> acctOverseaList = acctOverseaService.queryByCompanyId(companyId);
        initOverseaAcctBalance(acctOverseaList);

        Integer accountSubType = FundAccountSubType.BUSINESS_ACCOUNT.getKey();
        boolean companySwitch = uCompanySwitchService.isCompanySwitch(companyId);
        //不在白名单 是商务账户 在白名单 在区分平台方
        if (companySwitch) {
            accountSubType = FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey();
        }
        List<FundFreezen> fundFreezens = uFundFreezenService.queryByCompanyIdAndUseTypes(companyId, accountSubType, Arrays.asList(FundAccountModelType.RECHARGE.getKey(), FundAccountModelType.CREDIT.getKey()), FreezenUseType.bankFreezenInt());
        accountGenerals.stream().filter(Objects::nonNull).forEach(general -> {
            //充值主体
            AcctDebitMainRespDTO debitMainRespDTO = AcctMainConvert.buildAcctDebitMainRespDTO(businessDebitShow, individualDebitsShow, acctCompanyCardsShow, publicRespDtosShow, fundFreezens, general, acctSettlements, acctReimbursementsShow, acctOverseaList);
            if (Objects.nonNull(debitMainRespDTO)) {
                acctDebitMainList.add(debitMainRespDTO);
            }
            //授信主体
            buildAcctCreditMainList(acctCreditMainList ,businessCreditsShow, individualCreditsShow, general);
        });
        BigDecimal generalBalanc = accountGenerals.stream().filter(Objects::nonNull).map(AccountGeneral::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        allBalance = allBalance.add(generalBalanc);

        List<AcctDebitMainRespDTO> acctDebitMainListSort = acctDebitMainRespSort(acctDebitMainList);
        //红包券
        AccountRedcouponInfoVO redCouponInfo = accountRedcouponManager.queryAccountCouponInfoVO(companyId);

        respDTO.setCompanyId(companyId);
        respDTO.setAllBalance(allBalance);
        respDTO.setAcctDebitMainRespDTOList(acctDebitMainListSort);
        respDTO.setAcctCreditMainRespDTOList(acctCreditMainList);
        respDTO.setAccountRedcouponInfoVO(redCouponInfo);
        
        List<AcctBankEntMainRespDTO> ents = null;
        try {
        	ents = future.get();
		} catch (Exception e) {} 
        
        respDTO.setAcctBankEntRespDTOList(ents);

        return respDTO;
    }

    public List<AcctBankEntMainRespDTO> addProperties4BankEnt(String companyId){
        try {
            BankEntAccountInfoReqDTO bankEntAccountInfoReqDTO = new BankEntAccountInfoReqDTO();
            bankEntAccountInfoReqDTO.setCompanyId(companyId);
            BankEntAccountInfoRespDTO bankEntAccountInfoRespDTO = bankEntAccountInfoService.queryAccountInfo4Overview(bankEntAccountInfoReqDTO);
            List<AcctBankEntMainRespDTO> acctBankEntMainRespDTOS = new ArrayList<>();
            if (bankEntAccountInfoRespDTO!= null
                    && CollectionUtils.isNotEmpty(bankEntAccountInfoRespDTO.getBankEntAccountInfoDTOS())) {
                for (BankEntAccountInfoDTO bankEntAccountInfoDTO:bankEntAccountInfoRespDTO.getBankEntAccountInfoDTOS()) {
                    if(bankEntAccountInfoDTO.getAccountStatus() == 1) {
                        AcctBankEntMainRespDTO acctBankEntMainRespDTO = new AcctBankEntMainRespDTO();
                        acctBankEntMainRespDTO.setAcctInfoId(bankEntAccountInfoDTO.getAcctInfoId());
                        acctBankEntMainRespDTO.setCompanyId(bankEntAccountInfoDTO.getCompanyId());
                        acctBankEntMainRespDTO.setCompanyName(bankEntAccountInfoDTO.getCompanyName());
                        acctBankEntMainRespDTO.setCompanyMainId(bankEntAccountInfoDTO.getCompanyMainId());
                        acctBankEntMainRespDTO.setCompanyMainType(bankEntAccountInfoDTO.getCompanyMainType());
                        acctBankEntMainRespDTO.setBankName(bankEntAccountInfoDTO.getBankName());
                        acctBankEntMainRespDTO.setBankCode(bankEntAccountInfoDTO.getBankCode());
                        acctBankEntMainRespDTO.setPlatformCode(bankEntAccountInfoDTO.getPlatformCode());
                        acctBankEntMainRespDTO.setPlatformCodeName(BankEntPlatformCodeEnum.getEnum(bankEntAccountInfoDTO.getPlatformCode()).getPlatformCodeDesc());
                        acctBankEntMainRespDTO.setBankAccountNo(bankEntAccountInfoDTO.getBankAccountNo());
                        acctBankEntMainRespDTO.setBankAccountName(bankEntAccountInfoDTO.getBankAccountName());
                        acctBankEntMainRespDTO.setBankAccountKind(bankEntAccountInfoDTO.getBankAccountKind());
                        acctBankEntMainRespDTO.setAccountStatus(bankEntAccountInfoDTO.getAccountStatus());
                        if(Objects.nonNull(bankEntAccountInfoDTO.getAccountBalance())) {
                            acctBankEntMainRespDTO.setAccountBalance(BigDecimalUtils.yuan2fen(bankEntAccountInfoDTO.getAccountBalance()));
                        }
                        if(Objects.nonNull(bankEntAccountInfoDTO.getAvailableBalance())) {
                            acctBankEntMainRespDTO.setAvailableBalance(BigDecimalUtils.yuan2fen(bankEntAccountInfoDTO.getAvailableBalance()));
                        }
                        if(Objects.nonNull(bankEntAccountInfoDTO.getFrozenBalance())) {
                            acctBankEntMainRespDTO.setFrozenBalance(BigDecimalUtils.yuan2fen(bankEntAccountInfoDTO.getFrozenBalance()));
                        }
                        acctBankEntMainRespDTO.setBankIconWebSmall(bankEntAccountInfoDTO.getBankIconWebSmall());
                        acctBankEntMainRespDTO.setBankIconWebBig(bankEntAccountInfoDTO.getBankIconWebBig());
                        acctBankEntMainRespDTO.setBankBackground(bankEntAccountInfoDTO.getBankBackground());
                        if(!StringUtils.isBlank(bankEntAccountInfoDTO.getUpdateTime())) {
                            acctBankEntMainRespDTO.setUpdateTime(DateUtils.parseTime(bankEntAccountInfoDTO.getUpdateTime()));
                        }
                        acctBankEntMainRespDTOS.add(acctBankEntMainRespDTO);
                    }
                }
            }
            return acctBankEntMainRespDTOS;
        }catch (Exception e){
            FinhubLogger.error("queryAccountInfo4Overview error",e);
            return null;
        }
    }

    /**
     *  老版本:【企业账户】-【账户管理】
     *  账户5.0:【账户服务】- 【电子账户服务】
     * @param reqDTO
     * @return
     */
    @Override
    public AcctOverviewSimpleRespDTO queryAcctOverviewMgr(AcctOverviewReqDTO reqDTO) {
        String companyId = reqDTO.getCompanyId();
        AcctOverviewSimpleRespDTO respDTO = new AcctOverviewSimpleRespDTO(companyId);
        List<AcctDebitMainSimpleRespDTO> acctDebitMainList = new ArrayList<>();
        List<AcctCreditMainSimpleRespDTO> acctCreditMainList = new ArrayList<>();

        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return respDTO;
        }

        List<AcctReimbursement> acctReimbursements = uAcctReimbursementService.findByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(acctReimbursements)){
            acctReimbursements = acctReimbursements.stream()
                    .filter(acctReimbursement -> FundAcctShowStatusEnum.isShow(acctReimbursement.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctReimbursement> acctReimbursementsShow = acctReimbursements;

        List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(businessDebits)){
            businessDebits = businessDebits.stream()
                    .filter(acctBusinessDebit -> FundAcctShowStatusEnum.isShow(acctBusinessDebit.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctBusinessDebit> businessDebitsShow = businessDebits;

        List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(individualDebits)){
            individualDebits = individualDebits.stream()
                    .filter(acctIndividualDebit -> FundAcctShowStatusEnum.isShow(acctIndividualDebit.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctIndividualDebit> individualDebitsShow = individualDebits;

        List<AcctBusinessCredit> businessCredits = uAcctBusinessCreditService.findByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(businessCredits)){
            businessCredits = businessCredits.stream()
                    .filter(acctBusinessCredit -> FundAcctShowStatusEnum.isShow(acctBusinessCredit.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctBusinessCredit> businessCreditsShow = businessCredits;

        List<AcctIndividualCredit> individualCredits = uAcctIndividualCreditService.findByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(individualCredits)){
            individualCredits = individualCredits.stream()
                    .filter(acctIndividualCredit -> FundAcctShowStatusEnum.isShow(acctIndividualCredit.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctIndividualCredit> individualCreditsShow = individualCredits;

        List<AcctCompanyCard> acctCompanyCards = uAcctCompanyCardService.findByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(acctCompanyCards)){
            acctCompanyCards = acctCompanyCards.stream()
                    .filter(acctCompanyCard -> FundAcctShowStatusEnum.isShow(acctCompanyCard.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctCompanyCard> acctCompanyCardsShow =acctCompanyCards;

        List<AcctPublicDetailRespDTO> publicRespDtos = acctPublicSearchService.findByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(publicRespDtos)){
            publicRespDtos = publicRespDtos.stream()
                    .filter(publicRespDto -> FundAcctShowStatusEnum.isShow(publicRespDto.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctPublicDetailRespDTO> publicRespDtosShow = publicRespDtos;
        
        List<AcctOversea> acctOverseas = acctOverseaService.queryByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(acctOverseas)){
        	acctOverseas = acctOverseas.stream()
                    .filter(oa -> FundAcctShowStatusEnum.isShow(oa.getShowStatus()))
                    .collect(Collectors.toList());
        }
        List<AcctOversea> oas = acctOverseas;

        accountGenerals.stream().filter(Objects::nonNull).forEach(general -> {

            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(general.getCompanyId(), general.getCompanyMainId(), general.getBankName());
            //充值主体
            AcctDebitMainSimpleRespDTO debitMainRespDTO = AcctMainConvert.makAcctDebitMainSimpleRespDTO(businessDebitsShow, individualDebitsShow, acctCompanyCardsShow, publicRespDtosShow, general,acctReimbursementsShow, oas, acctCompanyMain);
            if (Objects.nonNull(debitMainRespDTO)) {
                /**
                 * ZHIFU-5070 中信账户名称优化
                 * 1.上线之后展示主体名称
                 * 2.白名单内展示主体名称
                 */
                String showMainName = getShowMainName(general.getBankName(), general.getCompanyMainName(), general.getBankAccountNo(), acctCompanyMain);
                debitMainRespDTO.setCompanyMainName(showMainName);
                acctDebitMainList.add(debitMainRespDTO);
            }
            //授信主体
            AcctCreditMainSimpleRespDTO creditMainRespDTO = AcctMainConvert.makeAcctCreditMainSimpleRespDTO(businessCreditsShow, individualCreditsShow, general, acctCompanyMain);
            if (Objects.nonNull(creditMainRespDTO)) {
                /**
                 * ZHIFU-5070 中信账户名称优化
                 * 1.上线之后展示主体名称
                 * 2.白名单内展示主体名称
                 */
                String showMainName = getShowMainName(general.getBankName(), general.getCompanyMainName(), general.getBankAccountNo(), acctCompanyMain);
                creditMainRespDTO.setCompanyMainName(showMainName);
                acctCreditMainList.add(creditMainRespDTO);
            }
        });
        respDTO.setAcctDebitMainSimpleRespDTOList(acctDebitMainList);
        respDTO.setAcctCreditMainSimpleRespDTOList(acctCreditMainList);
        return respDTO;
    }

    @Override
    public List<AcctGroupOverviewSimpleRespDto> queryGroupAcctOverviewMgr(List<CompanyAccountInfo> companyAccountInfos) {
        List<AcctGroupOverviewSimpleRespDto> respDtos = new ArrayList<>();
        for (CompanyAccountInfo comAuthInfo : companyAccountInfos) {
            CompanyAccountInfo.AcctAuthEnum acctAuthEnum = comAuthInfo.getAcctAuthEnum();
            if (CompanyAccountInfo.AcctAuthEnum.NO_AUTH.equals(acctAuthEnum)) {
                continue;
            }

            AcctOverviewReqDTO acctOverviewReqDTO = new AcctOverviewReqDTO(comAuthInfo.getCompanyId());
            AcctOverviewSimpleRespDTO acctOverviewSimpleRespDTO = queryAcctOverviewMgr(acctOverviewReqDTO);
            AcctGroupOverviewSimpleRespDto respDto = new AcctGroupOverviewSimpleRespDto();
            BeanUtils.copyProperties(acctOverviewSimpleRespDTO, respDto);
            respDto.setCompanyGroupType(comAuthInfo.getCompanyType());
            respDto.setCompanyAcctShowName(comAuthInfo.getCompanyName());
            respDtos.add(respDto);

            if (CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA.equals(acctAuthEnum)) {
                continue;
            }

            //用户在该企业有权限的账户集合
            List<BaseAccountVO> authAccounts = comAuthInfo.getPermissionAccounts();
            Map<String, List<BaseAccountVO>> authAccountMap = authAccounts.stream().collect(Collectors.groupingBy(BaseAccountVO::getAccountId));

            List<AcctDebitMainSimpleRespDTO> acctDebitRespDTOList = acctOverviewSimpleRespDTO.getAcctDebitMainSimpleRespDTOList();
            if (CollectionUtils.isNotEmpty(acctDebitRespDTOList)) {
                Iterator<AcctDebitMainSimpleRespDTO> iterator = acctDebitRespDTOList.iterator();
                while (iterator.hasNext()) {
                    AcctDebitMainSimpleRespDTO acctDebitRespDTO = iterator.next();
                    List<BaseAccountVO> accountInfoVOS = authAccountMap.get(acctDebitRespDTO.getAccountGeneralId());
                    if (CollectionUtils.isEmpty(accountInfoVOS)) {
                        iterator.remove();
                        continue;
                    }
                    if (BankNameEnum.FBT.getCode().equals(acctDebitRespDTO.getBankName())) {
                        List<BaseAccountVO> rechargeAccountInfos = accountInfoVOS.stream()
                                .filter(accountInfoVO -> FundAccountModelType.isRecharge(accountInfoVO.getAccountModel())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(rechargeAccountInfos)) {
                            iterator.remove();
                        }
                    }
                }
                respDto.setAcctDebitMainSimpleRespDTOList(acctDebitRespDTOList);
            }

            List<AcctCreditMainSimpleRespDTO> acctCreditRespDTOList = acctOverviewSimpleRespDTO.getAcctCreditMainSimpleRespDTOList();
            if (CollectionUtils.isNotEmpty(acctCreditRespDTOList)) {
                AcctCreditMainSimpleRespDTO acctCreditRespDTO = acctCreditRespDTOList.get(0);
                if (acctCreditRespDTO != null) {
                    List<BaseAccountVO> accountInfoVOS = authAccountMap.get(acctCreditRespDTO.getAccountGeneralId());
                    if (CollectionUtils.isEmpty(accountInfoVOS)) {
                        respDto.setAcctCreditMainSimpleRespDTOList(new ArrayList<>());
                    } else {
                        List<BaseAccountVO> creditAccountInfos = accountInfoVOS.stream()
                                .filter(accountInfoVO -> FundAccountModelType.isCredit(accountInfoVO.getAccountModel())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(creditAccountInfos)) {
                            respDto.setAcctCreditMainSimpleRespDTOList(new ArrayList<>());
                        }
                    }
                }
            }

        }
        return respDtos.stream().sorted(Comparator.comparing(AcctGroupOverviewSimpleRespDto::getCompanyGroupType)).collect(Collectors.toList());
    }

    @Override
    public AcctCompanyMainDetailRespDTO queryCompanyMainDetailInfo(AcctComGwByMIdBankReqDTO reqDTO) {
        AcctCompanyMainDetailRespDTO respDTO = new AcctCompanyMainDetailRespDTO();
        AccountGeneral acctGeneral = uAcctGeneralService.findByComIdAndMIdAndBank(reqDTO.getCompanyId(), reqDTO.getCompanyMainId(), reqDTO.getBankName(), reqDTO.getBankAccountNo());
        BeanUtils.copyProperties(acctGeneral, respDTO);
        if (BankNameEnum.isFbt(reqDTO.getBankName())) {
            respDTO.setCompanyMainName(acctGeneral.getCompanyMainName());
        } else {
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(reqDTO.getCompanyId(), reqDTO.getCompanyMainId(), reqDTO.getBankName());
            if (Objects.isNull(acctCompanyMain)) {
                throw new FinPayException(GlobalResponseCode.MAIN_COMPANY_NO_EXIST);
            }
            BeanUtils.copyProperties(acctCompanyMain, respDTO);
            respDTO.setCompanyMainName(StringUtils.isBlank(acctCompanyMain.getBankBusinessName()) ? acctCompanyMain.getBusinessName() : acctCompanyMain.getBankBusinessName());
            respDTO.setBankAccountName(acctCompanyMain.getBusinessName());
            // 支行名
            if(!BankBranchNameEnum.getBankEnum(acctGeneral.getBankName()).equals(BankBranchNameEnum.UN_KNOW)){
                respDTO.setBankBranchName(BankBranchNameEnum.getBankEnum(acctGeneral.getBankName()).getBankBranchName());
            }

            List<AcctCompanyMainDetailRespDTO.BindCard> bindCards = buildBindCardList(acctGeneral.getCompanyMainId(), acctGeneral.getBankName(), acctCompanyMain);
            respDTO.setBindCardList(bindCards);
        }
        return respDTO;
    }

    @Override
    public AcctCompanyMainDetailRespDTO queryCompanyMainDetailInfo4Recharge(AcctComGwByMIdBankReqDTO reqDTO) {
        AcctCompanyMainDetailRespDTO respDTO = new AcctCompanyMainDetailRespDTO();
        AccountGeneral acctGeneral = uAcctGeneralService.findByComIdAndMIdAndBank(reqDTO.getCompanyId(), reqDTO.getCompanyMainId(), reqDTO.getBankName(), reqDTO.getBankAccountNo());
        BeanUtils.copyProperties(acctGeneral, respDTO);
        if (BankNameEnum.isFbt(reqDTO.getBankName())) {
            respDTO.setCompanyMainName(acctGeneral.getCompanyMainName());
        } else {
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(reqDTO.getCompanyId(), reqDTO.getCompanyMainId(), reqDTO.getBankName());
            if (Objects.isNull(acctCompanyMain)) {
                throw new FinPayException(GlobalResponseCode.MAIN_COMPANY_NO_EXIST);
            }
            BeanUtils.copyProperties(acctCompanyMain, respDTO);
            respDTO.setCompanyMainName(StringUtils.isBlank(acctCompanyMain.getBankBusinessName()) ? acctCompanyMain.getBusinessName() : acctCompanyMain.getBankBusinessName());
            respDTO.setBankAccountName(acctCompanyMain.getBusinessName());
            // 支行名
            if(!BankBranchNameEnum.getBankEnum(acctGeneral.getBankName()).equals(BankBranchNameEnum.UN_KNOW)){
                respDTO.setBankBranchName(BankBranchNameEnum.getBankEnum(acctGeneral.getBankName()).getBankBranchName());
            }

            List<AcctCompanyMainDetailRespDTO.BindCard> bindCards = buildBindCardList4Recharge(acctGeneral.getCompanyMainId(), acctGeneral.getBankName(), acctCompanyMain);
            respDTO.setBindCardList(bindCards);
        }
        return respDTO;
    }

    private List<AcctCompanyMainDetailRespDTO.BindCard> buildBindCardList(String companyMainId, String bankName, AcctCompanyMain acctCompanyMain) {
        List<AcctCompanyMainDetailRespDTO.BindCard> bindCardList = Lists.newArrayList();
        List<AcctCompanyBindCard> acctCompanyBindCardList = acctCompanyBindCardService.queryByCompanyMainId(companyMainId);
        // 目前只有平安银行支持绑定多个银行卡，AcctCompanyBindCard只存储平安银行绑定信息，其他银行绑定还是在acctCompanyMain中
        if ((BankNameEnum.isSpa(bankName) || BankNameEnum.isCitic(bankName)) && CollectionUtils.isNotEmpty(acctCompanyBindCardList)) {
            if(BankNameEnum.isCitic(bankName)){
                bindCardList = acctCompanyBindCardList.stream().filter(x-> acctCompanyMain.getBusinessName().equals(x.getBusinessName())
                        ).map(x->AcctCompanyMainDetailRespDTO.BindCard.builder()
                                .bankCardNo(x.getBankCardNo()).bankCardName(x.getBankCardName()).bankAccountName(x.getBusinessName()).build())
                        .collect(Collectors.toList());
            }else {
                bindCardList = acctCompanyBindCardList.stream().map(x->AcctCompanyMainDetailRespDTO.BindCard.builder()
                                .bankCardNo(x.getBankCardNo()).bankCardName(x.getBankCardName()).bankAccountName(x.getBusinessName()).build())
                        .collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(bindCardList)) {
            AcctCompanyMainDetailRespDTO.BindCard bindCard = new AcctCompanyMainDetailRespDTO.BindCard();
            bindCard.setBankCardNo(acctCompanyMain.getBankCardNo());
            bindCard.setBankCardName(acctCompanyMain.getBankCardName());
            bindCard.setBankAccountName(acctCompanyMain.getBusinessName());
            bindCardList.add(bindCard);
        }
        return bindCardList;
    }

    private List<AcctCompanyMainDetailRespDTO.BindCard> buildBindCardList4Recharge(String companyMainId, String bankName, AcctCompanyMain acctCompanyMain) {
        List<AcctCompanyMainDetailRespDTO.BindCard> bindCardList = Lists.newArrayList();
        List<AcctCompanyBindCard> acctCompanyBindCardList = acctCompanyBindCardService.queryByCompanyMainId(companyMainId);
        // 目前只有平安银行支持绑定多个银行卡，AcctCompanyBindCard只存储平安银行绑定信息，其他银行绑定还是在acctCompanyMain中
        if ((BankNameEnum.isSpa(bankName) || BankNameEnum.isCitic(bankName)) && CollectionUtils.isNotEmpty(acctCompanyBindCardList)) {
            bindCardList = acctCompanyBindCardList.stream().map(x->AcctCompanyMainDetailRespDTO.BindCard.builder()
                                .bankCardNo(x.getBankCardNo()).bankCardName(x.getBankCardName()).bankAccountName(x.getBusinessName()).build())
                        .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(bindCardList)) {
            AcctCompanyMainDetailRespDTO.BindCard bindCard = new AcctCompanyMainDetailRespDTO.BindCard();
            bindCard.setBankCardNo(acctCompanyMain.getBankCardNo());
            bindCard.setBankCardName(acctCompanyMain.getBankCardName());
            bindCard.setBankAccountName(acctCompanyMain.getBusinessName());
            bindCardList.add(bindCard);
        }
        return bindCardList;
    }

    @Override
    public List<AcctBaseMainRespDTO> queryMainAcctView(AcctOverviewReqDTO reqDTO) {
        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(reqDTO.getCompanyId());
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return Collections.emptyList();
        }
        String companyName = accountGenerals.get(0).getCompanyName();
        List<AcctBaseMainRespDTO> acctBaseMainRespDTOS = Lists.newArrayList();
        for (AccountGeneral accountGeneral : accountGenerals) {
            AcctBaseMainRespDTO acctBaseMainRespDTO = new AcctBaseMainRespDTO();
            BeanUtils.copyProperties(accountGeneral, acctBaseMainRespDTO);
            acctBaseMainRespDTO.setAccountId(accountGeneral.getAccountGeneralId());
            acctBaseMainRespDTO.setAcctWebSelectKey(accountGeneral.getAccountGeneralId());
            // 返回是否下载交易电子回单
            acctBaseMainRespDTO.setDownloadTradeFlow(accountGeneral.getBankName());
            acctBaseMainRespDTOS.add(acctBaseMainRespDTO);

            if (FundPlatformEnum.isFBTPlatform(accountGeneral.getBankName())) {
                //拷贝一个分贝通的的授信帐户下拉平台卡
                if (FundAccountModelType.isRecharge(accountGeneral.getAccountModel())) {
                    AcctBusinessCredit businessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    AcctIndividualCredit individualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    if ((Objects.nonNull(businessCredit) && FundAcctShowStatusEnum.isShow(businessCredit.getShowStatus()))
                            || (Objects.nonNull(individualCredit) && FundAcctShowStatusEnum.isShow(individualCredit.getShowStatus()))) {
                        AcctBaseMainRespDTO copayAcctFbt = new AcctBaseMainRespDTO();
                        BeanUtils.copyProperties(accountGeneral, copayAcctFbt);
                        copayAcctFbt.setAccountId(accountGeneral.getAccountGeneralId());
                        copayAcctFbt.setAcctWebSelectKey(accountGeneral.getAccountGeneralId() + CREDIT.getKey());
                        copayAcctFbt.setAccountModel(CREDIT.getKey());
                        acctBaseMainRespDTOS.add(copayAcctFbt);
                    }
                }
                //拷贝一个分贝通的的充值帐户下拉平台卡
                if (FundAccountModelType.isCredit(accountGeneral.getAccountModel())) {
                    //查询
                    AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    AcctIndividualDebit companyIdAndBank = uAcctIndividualDebitService.findCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    if ((Objects.nonNull(acctBusinessDebit) && FundAcctShowStatusEnum.isShow(acctBusinessDebit.getShowStatus()))
                            || (Objects.nonNull(companyIdAndBank) && FundAcctShowStatusEnum.isShow(companyIdAndBank.getShowStatus()))) {
                        AcctBaseMainRespDTO copayAcctFbt = new AcctBaseMainRespDTO();
                        BeanUtils.copyProperties(accountGeneral, copayAcctFbt);
                        copayAcctFbt.setAccountId(accountGeneral.getAccountGeneralId());
                        copayAcctFbt.setAcctWebSelectKey(accountGeneral.getAccountGeneralId() + RECHARGE.getKey());
                        copayAcctFbt.setAccountModel(RECHARGE.getKey());
                        acctBaseMainRespDTOS.add(copayAcctFbt);
                    }
                }
            }
        }
        //红包券
        AccountRedcoupon accountRedcoupon = accountRedcouponManager.queryAccountRedcouponByCompanyId(reqDTO.getCompanyId());
        if (Objects.nonNull(accountRedcoupon)) {
            AcctBaseMainRespDTO acctBaseMainRespDTO = new AcctBaseMainRespDTO();
            BeanUtils.copyProperties(accountRedcoupon, acctBaseMainRespDTO);
            acctBaseMainRespDTO.setCompanyName(companyName);
            acctBaseMainRespDTO.setAccountId(accountRedcoupon.getAccountRedcouponId());
            acctBaseMainRespDTO.setAcctWebSelectKey(accountRedcoupon.getAccountRedcouponId());
            acctBaseMainRespDTOS.add(acctBaseMainRespDTO);
        }
        return acctBaseMainRespDTOS;
    }

    @Override
    public void changeCompanyName(AcctChangeNameReqDTO changeNameReqDTO) {
        //校验字段
        boolean resultGen = uAcctGeneralService.changeCompanyName(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean resultBusDebit = uAcctBusinessDebitService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean resultBusCredit = uAcctBusinessCreditService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean resultInvCredit = uAcctIndividualCreditService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean resultInvDebit = uAcctIndividualDebitService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean companyCard = uAcctCompanyCardService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean acctPublic = acctPublicService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean acctReimbursement = acctReimbursementService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        boolean overseaAcctUpdated = acctOverseaService.updateNameByCompanyId(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        // 修改主体企业名称
        boolean main = uAcctCompanyMainService.changeCompanyName(changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
        /*
          -- UC进件企业,CSM可以更改企业名称（只能改分贝通体系）,调用fb_pay接口
          -- 修改： fbt 体系改company_name ,也要改company_main_name,
          -- 银行体系只改company_name ,不改company_main_name,
         */
        boolean changeCompanyMainNameFlag = uAcctGeneralService.changeCompanyMainName(changeNameReqDTO.getCompanyId(),BankNameEnum.FBT.getCode(), changeNameReqDTO.getCompanyName());
        boolean isAllSuc = resultGen && changeCompanyMainNameFlag
                && resultBusDebit && resultBusCredit
                && resultInvCredit && resultInvDebit
                && companyCard
                && acctPublic
                && acctReimbursement 
                && overseaAcctUpdated;
        if(!isAllSuc){
            String msg = "账户名称变更,余额："+resultGen
                    + ",余额主体："+ changeCompanyMainNameFlag
                    + ",商务充值:" + resultBusDebit + ",商务授信:" + resultBusCredit
                    + ",个人充值:" + resultInvDebit + ",个人授信:" + resultInvCredit
                    + ",对公:" + acctPublic + ",报销:" + acctReimbursement + ",虚拟卡:" + companyCard;
            FinhubLogger.error("UC进件企业,CSM可以更改企业名称失败（只能改分贝通体系）：" + changeNameReqDTO.getCompanyId(), changeNameReqDTO.getCompanyName());
            dingDingMsgService.sendMsg("企业名称变更（只能改分贝通体系）," +msg + ","+ changeNameReqDTO.getCompanyId()+","+ changeNameReqDTO.getCompanyName());
        }
    }

    @Override
    public BigDecimal queryAccountOperationAmountByBizNo(String bizNo, int accountSubType, int accountSubOperationType) {
        BigDecimal totalOperationAmount = BigDecimal.ZERO;
        if (FundAccountSubType.isBusinessAccount(accountSubType)) {
            totalOperationAmount = acctBusinessDebitFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            if (totalOperationAmount.compareTo(BigDecimal.ZERO) == 0) {
                totalOperationAmount = acctBusinessCreditFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubType)) {
            totalOperationAmount = acctIndividualDebitFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            if (totalOperationAmount.compareTo(BigDecimal.ZERO) == 0) {
                totalOperationAmount = acctIndividualCreditFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            }
        }
        return totalOperationAmount;
    }

    @Override
    public AccountSubFlowRespRPCDTO queryAccountSubFlowByGrantTaskId(String voucherGrantTaskId, int accountSubTyp) {
        AccountSubFlowRespRPCDTO accountSubType = new AccountSubFlowRespRPCDTO();
        if (FundAccountSubType.isBusinessAccount(accountSubTyp)) {
            AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(acctBusinessDebitFlow)) {
                BeanUtils.copyProperties(acctBusinessDebitFlow, accountSubType);
                return accountSubType;
            }
            AcctBusinessCreditFlow acctBusinessCreditFlow = acctBusinessCreditFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(acctBusinessCreditFlow)) {
                BeanUtils.copyProperties(acctBusinessCreditFlow, accountSubType);
                return accountSubType;
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubTyp)) {
            AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(acctIndividualDebitFlow)) {
                BeanUtils.copyProperties(acctIndividualDebitFlow, accountSubType);
                return accountSubType;
            }
            AcctIndividualCreditFlow individualCreditFlow = acctIndividualCreditFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(individualCreditFlow)) {
                BeanUtils.copyProperties(individualCreditFlow, accountSubType);
                return accountSubType;
            }
        }
        return null;
    }

    @Override
    public AccountSubFlowRespRPCDTO queryAccountSubFlowByFlowId(String accountSubFlowId) {
        AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
        AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(acctIndividualDebitFlow)) {
            BeanUtils.copyProperties(acctIndividualDebitFlow, accountSubFlowRespRPCDTO);
            return accountSubFlowRespRPCDTO;
        }
        AcctIndividualCreditFlow individualCreditFlow = acctIndividualCreditFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(individualCreditFlow)) {
            BeanUtils.copyProperties(individualCreditFlow, accountSubFlowRespRPCDTO);
            return accountSubFlowRespRPCDTO;
        }
        AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(acctBusinessDebitFlow)) {
            BeanUtils.copyProperties(acctBusinessDebitFlow, accountSubFlowRespRPCDTO);
            return accountSubFlowRespRPCDTO;
        }
        AcctBusinessCreditFlow acctBusinessCreditFlow = acctBusinessCreditFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(acctBusinessCreditFlow)) {
            BeanUtils.copyProperties(acctBusinessCreditFlow, accountSubFlowRespRPCDTO);
            return accountSubFlowRespRPCDTO;
        }
        return null;
    }

    @Override
    public void unActivateBusinessAcct(AcctUpdateCommonReqDTO reqDTO) {
        AcctBusinessDebit unActivateAccountSub = acctBusinessDebitService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(), FundAcctActStatusEnum.ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        if (Objects.nonNull(unActivateAccountSub)) {
            if (Objects.equals(reqDTO.getAccountId(), unActivateAccountSub.getAccountId())) {
                return;
            }
            //失效帐户
            acctBusinessDebitService.updateActivateStatusByAccountSubId(unActivateAccountSub.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            AcctBusinessDebitMflow buDebitUnActivateMflow = AcctBusinessMFlowConvert.saveAcctBusinessDebitFlow(reqDTO, unActivateAccountSub, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                    FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            acctBusinessDebitMflowService.saveAccountBusinessDebitManagementFlow(buDebitUnActivateMflow);
            return;
        }
        AcctBusinessCredit acctBusinessCredit = acctBusinessCreditService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(), FundAcctActStatusEnum.ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        //网关查询
        AcctComGwByAcctTypeReqDTO acctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctTypeReqDTO.setFundAccountType(reqDTO.getAccountSubType());
        acctTypeReqDTO.setCompanyId(reqDTO.getCompanyId());
        AcctComGwAcctRespDTO actGwByAcctType = acctCompanyGatewayService.findActGwByAcctType(acctTypeReqDTO);
        //如果到这一步没查询到就抛错
        if (Objects.isNull(acctBusinessCredit) && Objects.nonNull(actGwByAcctType)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
        }
        if (Objects.equals(reqDTO.getAccountId(), acctBusinessCredit.getAccountId())) {
            return;
        }
        //激活默认显示
        acctBusinessCreditService.updateActivateStatusByAccountSubId(acctBusinessCredit.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        AcctBusinessCreditMflow buDebitUnActivateMflow = AcctBusinessMFlowConvert.saveAcctBusinessCreditFlow(reqDTO, acctBusinessCredit, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        acctBusinessCreditMflowService.saveAccountBusinessCreditManagementFlow(buDebitUnActivateMflow);
    }

    @Override
    public void unActivateIndividualAcct(AcctUpdateCommonReqDTO reqDTO) {
        AcctIndividualDebit unActivateAccountSub = acctIndividualDebitService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(),
                FundAcctActStatusEnum.ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        if (Objects.nonNull(unActivateAccountSub)) {
            //失效帐户
            acctIndividualDebitService.updateActivateStatusByAccountSubId(unActivateAccountSub.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            AcctIndividualDebitMflow acctIndividualDebitMflow1 = AcctIndividualDebitFlowConvert.saveAcctIndividualDebitFlow(reqDTO, unActivateAccountSub, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                    FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            acctIndividualDebitMflowService.saveAccountPersonDebitManagementFlow(acctIndividualDebitMflow1);
            return;
        }
        
        AcctIndividualCredit acctIndividualCredit = acctIndividualCreditService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(),
                ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        //网关查询
        AcctComGwByAcctTypeReqDTO acctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctTypeReqDTO.setFundAccountType(reqDTO.getAccountSubType());
        acctTypeReqDTO.setCompanyId(reqDTO.getCompanyId());
        AcctComGwAcctRespDTO acctRespDTO = acctCompanyGatewayService.findActGwByAcctType(acctTypeReqDTO);
        //如果到这一步没查询到就抛错
        if (Objects.isNull(acctIndividualCredit) && Objects.nonNull(acctRespDTO)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_EXIST);
        }
        //失效帐户
        acctIndividualCreditService.updateActivateStatusByAccountSubId(acctIndividualCredit.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        AcctIndividualCreditMflow acctIndividualDebitMFlow1 = AcctIndividualCreditFlowConvert.saveAcctIndividualCreditFlow(reqDTO, acctIndividualCredit, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        acctIndividualCreditMflowService.saveAcctMFlow(acctIndividualDebitMFlow1);
    }

    @Override
    public void unActivateOldBusinessAcct(AccountSubAbleReqRPCDTO reqDTO) {
        AcctBusinessDebit unActivateAccountSub = acctBusinessDebitService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(), FundAcctActStatusEnum.ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        if (Objects.nonNull(unActivateAccountSub)) {
            //失效帐户
            acctBusinessDebitService.updateActivateStatusByAccountSubId(unActivateAccountSub.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            AcctBusinessDebitMflow buDebitUnActivateMflow = AcctBusinessMFlowConvert.saveAcctOldBusinessDebitFlow(reqDTO, unActivateAccountSub, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                    FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            acctBusinessDebitMflowService.saveAccountBusinessDebitManagementFlow(buDebitUnActivateMflow);
            return;
        }
        
        AcctBusinessCredit acctBusinessCredit = acctBusinessCreditService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(), FundAcctActStatusEnum.ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        //网关查询
        AcctComGwByAcctTypeReqDTO acctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctTypeReqDTO.setFundAccountType(reqDTO.getAccountSubType());
        acctTypeReqDTO.setCompanyId(reqDTO.getCompanyId());
        AcctComGwAcctRespDTO actGwByAcctType = acctCompanyGatewayService.findActGwByAcctType(acctTypeReqDTO);
        //如果到这一步没查询到就抛错
        if (Objects.isNull(acctBusinessCredit) && Objects.nonNull(actGwByAcctType)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
        }
        //激活默认显示
        acctBusinessCreditService.updateActivateStatusByAccountSubId(acctBusinessCredit.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        AcctBusinessCreditMflow buDebitUnActivateMflow = AcctBusinessMFlowConvert.saveAcctOldBusinessCreditFlow(reqDTO, acctBusinessCredit, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        acctBusinessCreditMflowService.saveAccountBusinessCreditManagementFlow(buDebitUnActivateMflow);
    }

    @Override
    public void unActivateOldIndividualAcct(AccountSubAbleReqRPCDTO reqDTO) {
        AcctIndividualDebit unActivateAccountSub = acctIndividualDebitService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(),
                FundAcctActStatusEnum.ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        if (Objects.nonNull(unActivateAccountSub)) {
            //失效帐户
            acctIndividualDebitService.updateActivateStatusByAccountSubId(unActivateAccountSub.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            AcctIndividualDebitMflow acctIndividualDebitMflow1 = AcctIndividualDebitFlowConvert.saveAcctOldIndividualDebitFlow(reqDTO, unActivateAccountSub, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                    FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
            acctIndividualDebitMflowService.saveAccountPersonDebitManagementFlow(acctIndividualDebitMflow1);
            return;
        }
        AcctIndividualCredit acctIndividualCredit = acctIndividualCreditService.getAccountByActiveStatusAcctSubType(reqDTO.getCompanyId(),
                ACTIVATE.getStatus(), reqDTO.getAccountSubType());
        //网关查询
        AcctComGwByAcctTypeReqDTO acctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctTypeReqDTO.setFundAccountType(reqDTO.getAccountSubType());
        acctTypeReqDTO.setCompanyId(reqDTO.getCompanyId());
        AcctComGwAcctRespDTO acctRespDTO = acctCompanyGatewayService.findActGwByAcctType(acctTypeReqDTO);
        //如果到这一步没查询到就抛错
        if (Objects.isNull(acctIndividualCredit) && Objects.nonNull(acctRespDTO)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_EXIST);
        }
        //失效帐户
        acctIndividualCreditService.updateActivateStatusByAccountSubId(acctIndividualCredit.getAccountId(), FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        AcctIndividualCreditMflow acctIndividualDebitMflow1 = AcctIndividualCreditFlowConvert.saveAcctOldIndividualCreditFlow(reqDTO, acctIndividualCredit, FundAcctDebitOptType.UN_ACT_ACCOUNT,
                FundAcctActStatusEnum.UN_ACTIVATE, FundAcctShowStatusEnum.SHOW);
        acctIndividualCreditMflowService.saveAcctMFlow(acctIndividualDebitMflow1);
    }

    @Override
    public void changeCompanyModel(ChangeCompModelAndActAcctReqDTO respDTO) {
        try {
            uAccountGeneralService.changeCompanyModel(respDTO.getCompanyId(), respDTO.getCompanyModel());
            uAcctBusinessCreditService.updateModelByCompanyId(respDTO.getCompanyId(), respDTO.getCompanyModel());
            uAcctBusinessDebitService.updateModelByCompanyId(respDTO.getCompanyId(), respDTO.getCompanyModel());
            uAcctIndividualCreditService.updateModelByCompanyId(respDTO.getCompanyId(), respDTO.getCompanyModel());
            uAcctIndividualDebitService.updateModelByCompanyId(respDTO.getCompanyId(), respDTO.getCompanyModel());
            accountRedcouponService.changeCompanyModel(respDTO.getCompanyId(), respDTO.getCompanyModel());
            uAcctCompanyCardService.changeCompanyModel(respDTO.getCompanyId(), respDTO.getCompanyModel());
            acctCompanyGatewayService.changeCompanyModel(respDTO.getCompanyId(), respDTO.getCompanyModel());
            acctPublicDechService.changeAcctPublicCompanyModel(respDTO.getCompanyId(), respDTO.getCompanyModel());
            uAcctReimbursementService.changeCompanyModel(respDTO.getCompanyId(),respDTO.getCompanyModel());
        } catch (Exception e) {
            FinhubLogger.error("更新公司模式失败{}=={}", JsonUtils.toJson(respDTO), e);
            throw new FinPayException(GlobalResponseCode.EXCEPTION);
        }
    }

    @Override
    public void changeActivateAcct(List<AcctUpdateCommonReqDTO> respDTO) {
        List<AcctUpdateCommonReqDTO> companyCardList = new ArrayList<>();
        List<CompanyVirtualCardDTO> companyVirtualCardDTOS = Lists.newArrayList();
        for (AcctUpdateCommonReqDTO reqDTO : respDTO) {
            reqDTO.checkReq();
            AcctComGwUpdateReqDTO comGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
            if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType())) {
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    //失效当前的商务帐户
                    unActivateBusinessAcct(reqDTO);
                    AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.activateAcctBuDebitSub(reqDTO);
                    BeanUtils.copyProperties(acctBusinessDebit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctBusinessDebit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                } else {
                    //失效当前的商务帐户
                    unActivateBusinessAcct(reqDTO);
                    AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.activateAcctBuCreditSub(reqDTO);
                    BeanUtils.copyProperties(acctBusinessCredit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctBusinessCredit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                }
            } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType())) {
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    //失效当前的个人帐户
                    unActivateIndividualAcct(reqDTO);
                    AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.activateAcctInDebitSub(reqDTO);
                    BeanUtils.copyProperties(acctIndividualDebit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctIndividualDebit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);

                } else {
                    unActivateIndividualAcct(reqDTO);
                    AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.activateAcctInCreditSub(reqDTO);
                    BeanUtils.copyProperties(acctIndividualCredit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctIndividualCredit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                }
            } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey())) {
                companyCardList.add(reqDTO);
            } else if (FundAccountSubType.isOverseaAcct(reqDTO.getAccountSubType())) {
            	acctOverseaService.deactivateCurrentAccount(reqDTO);
            	AcctOversea overseaAcct = acctOverseaService.activateAccount(reqDTO.getAccountId());
            	BeanUtils.copyProperties(overseaAcct, comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                comGwUpdateReqDTO.setFundPlatform(getFundPlatform(overseaAcct.getBankName()));
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                
                CompanyVirtualCardDTO virtualCardDTO = new CompanyVirtualCardDTO();
                virtualCardDTO.setCardIssuerCode(overseaAcct.getBankName());
                virtualCardDTO.setCardIssuerName(BankNameEnum.getBankEnum(overseaAcct.getBankName()).getName());
                companyVirtualCardDTOS.add(virtualCardDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(companyCardList)) {
            List<AcctCompanyCard> acctCompanyCardList = uAcctCompanyCardService.activateCompanyCardSub4Multi(companyCardList);
            //一企业生效一个备用金账户
            if (acctCompanyCardList.size() == 1) {
                AcctComGwUpdateReqDTO comGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
                BeanUtils.copyProperties(acctCompanyCardList.get(0), comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
                FundPlatformEnum platformByName = FundPlatformEnum.findPlatFrom(acctCompanyCardList.get(0).getBankName(), FundAcctDirectAcctTypeEnum.BANK.getKey());
                comGwUpdateReqDTO.setFundPlatform(platformByName.getKey());
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
            }
            //改虚拟卡的生效帐户。需要通知UC改银行卡
            convertCompanyVirtualCard(acctCompanyCardList, companyVirtualCardDTOS);
        } else {
            if (respDTO.size() <=0){
                return;
            }
            //如果没有虚拟卡节点,说明把唯一生效的虚拟卡账户也取消了
            String companyId = respDTO.get(0).getCompanyId();
            String operationUserId = respDTO.get(0).getOperationUserId();
            uAcctCompanyCardService.disActiveCompanyCard(companyId,operationUserId);
        }
        if (CollectionUtils.isNotEmpty(companyVirtualCardDTOS)) {
        	String companyId = respDTO.get(0).getCompanyId();
            CompletableFuture.runAsync(() -> {
                try {
                    FinhubLogger.info("改虚拟卡的生效帐户,通知UC改银行卡companyId={}, cardDTOList={}", companyId,JsonUtils.toJson(companyVirtualCardDTOS));
                    iCompanyService.updateCompanyVirtualCardIssuerV2(companyId, companyVirtualCardDTOS);
                } catch (Exception e) {
                    dingDingMsgService.sendMsg("改虚拟卡的生效帐户。需要通知UC改银行卡失败：{}" + companyId);
                    FinhubLogger.error("改虚拟卡的生效帐户。需要通知UC改银行卡失败companyId={}, cardDTOList={}", companyId,JsonUtils.toJson(companyVirtualCardDTOS));
                }
            }, asyncExecutor);
        }
    }

    /**
     * 修改合作模式只需要改商务和个人,不需要改虚拟卡
     * @param respDTO
     */
    @Override
    public void changeActivateAcct4ChangeCompanyModel(List<AcctUpdateCommonReqDTO> respDTO) {
//        List<AcctUpdateCommonReqDTO> companyCardList = new ArrayList<>();
        for (AcctUpdateCommonReqDTO reqDTO : respDTO) {
            reqDTO.checkReq();
            AcctComGwUpdateReqDTO comGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
            if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType())) {
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    //失效当前的商务帐户
                    unActivateBusinessAcct(reqDTO);
                    AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.activateAcctBuDebitSub(reqDTO);
                    BeanUtils.copyProperties(acctBusinessDebit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctBusinessDebit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                } else {
                    //失效当前的商务帐户
                    unActivateBusinessAcct(reqDTO);
                    AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.activateAcctBuCreditSub(reqDTO);
                    BeanUtils.copyProperties(acctBusinessCredit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctBusinessCredit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                }
            } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType())) {
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    //失效当前的个人帐户
                    unActivateIndividualAcct(reqDTO);
                    AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.activateAcctInDebitSub(reqDTO);
                    BeanUtils.copyProperties(acctIndividualDebit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctIndividualDebit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);

                } else {
                    unActivateIndividualAcct(reqDTO);
                    AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.activateAcctInCreditSub(reqDTO);
                    BeanUtils.copyProperties(acctIndividualCredit, comGwUpdateReqDTO);
                    comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                    comGwUpdateReqDTO.setFundPlatform(getFundPlatform(acctIndividualCredit.getBankName()));
                    acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                }
            }
        }
    }

    private void convertCompanyVirtualCard(List<AcctCompanyCard> acctCompanyCardList, List<CompanyVirtualCardDTO> companyVirtualCardDTOS){
        acctCompanyCardList.forEach(p->{
            CompanyVirtualCardDTO virtualCardDTO = new CompanyVirtualCardDTO();
            virtualCardDTO.setCardIssuerCode(p.getBankChannelName());
            virtualCardDTO.setCardIssuerName(BankNameEnum.getBankEnum(p.getBankChannelName()).getName());
            companyVirtualCardDTOS.add(virtualCardDTO);
        });
    }

    private Integer getFundPlatform(String bankName){
        FundPlatformEnum platformByName;
        // 如果银行名称是FBT 则资金账户平台是 1
        if (BankNameEnum.isFbt(bankName)) {
            platformByName = FundPlatformEnum.FBT;
        } else {
            platformByName = FundPlatformEnum.findPlatFrom(bankName, FundAcctDirectAcctTypeEnum.BANK.getKey());
        }
        return platformByName.getKey();
    }

    @Override
    public BigDecimal queryAccountSubFlowByBizNo(String bizNo) {
        BigDecimal operationAmount;
        operationAmount = acctIndividualDebitFlowService.queryTotalConsumeAmountByOrderId(bizNo);
        if (Objects.nonNull(operationAmount)) {
            return operationAmount;
        }
        operationAmount = acctIndividualCreditFlowService.queryTotalConsumeAmountByOrderId(bizNo);
        if (Objects.nonNull(operationAmount)) {
            return operationAmount;
        }
        operationAmount = acctBusinessDebitFlowService.queryTotalConsumeAmountByOrderId(bizNo);
        if (Objects.nonNull(operationAmount)) {
            return operationAmount;
        }
        operationAmount = acctBusinessCreditFlowService.queryTotalConsumeAmountByOrderId(bizNo);
        if (Objects.nonNull(operationAmount)) {
            return operationAmount;
        }
        return null;
    }

    @Override
    public void changeactAivityAccountSub(AccountSubAbleReqRPCDTO reqDTO) {
        reqDTO.checkReq();
        AcctComGwUpdateReqDTO comGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
        if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType())) {
            if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                //失效当前的商务帐户
                unActivateOldBusinessAcct(reqDTO);
                AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.activateOldAcctBuDebitSub(reqDTO);
                BeanUtils.copyProperties(acctBusinessDebit, comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                comGwUpdateReqDTO.setFundPlatform(FundPlatformEnum.findPlatFrom(acctBusinessDebit.getBankName()).getKey());
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
            } else {
                //失效当前的商务帐户
                unActivateOldBusinessAcct(reqDTO);
                AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.activateOldAcctBuCreditSub(reqDTO);
                BeanUtils.copyProperties(acctBusinessCredit, comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                comGwUpdateReqDTO.setFundPlatform(FundPlatformEnum.findPlatFrom(acctBusinessCredit.getBankName()).getKey());
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
            }
        } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType())) {
            if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                //失效当前的个人帐户
                unActivateOldIndividualAcct(reqDTO);
                AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.activateOldAcctInDebitSub(reqDTO);
                BeanUtils.copyProperties(acctIndividualDebit, comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                comGwUpdateReqDTO.setFundPlatform(FundPlatformEnum.findPlatFrom(acctIndividualDebit.getBankName()).getKey());
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
            } else {
                unActivateOldIndividualAcct(reqDTO);
                AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.activateOldAcctInCreditSub(reqDTO);
                BeanUtils.copyProperties(acctIndividualCredit, comGwUpdateReqDTO);
                comGwUpdateReqDTO.setAccountSubType(reqDTO.getAccountSubType());
                comGwUpdateReqDTO.setFundPlatform(FundPlatformEnum.findPlatFrom(acctIndividualCredit.getBankName()).getKey());
                acctCompanyGatewayService.updateOrAddActGw(comGwUpdateReqDTO);
            }
        }
    }

    @Override
    public List<ZxTradeFlowPageRespDTO> zxTradeFlowPage(ZxTradeFlowPageReqDTO dto) {
        ZxQueryTradeFlowPageReqDTO reqDTO = new ZxQueryTradeFlowPageReqDTO();
        reqDTO.setSubAccNo(dto.getBankAccountNo());
        reqDTO.setStartDate(DateUtils.format(dto.getTradeStartTime(), DateUtils.FORMAT_DATE_YYYYMMDD));
        reqDTO.setEndDate(DateUtils.format(dto.getTradeEndTime(), DateUtils.FORMAT_DATE_YYYYMMDD));
        List<ZxQueryTradeFlowPageRespDto> zxQueryTradeFlowPageRespDtos = iZxBankAccountService.queryTradeFlowPage(reqDTO);
        List<ZxTradeFlowPageRespDTO> respDTOS = zxQueryTradeFlowPageRespDtos.stream().map(s -> {
            ZxTradeFlowPageRespDTO result = new ZxTradeFlowPageRespDTO();
            BeanUtils.copyProperties(s, result);
            return result;
        }).collect(Collectors.toList());
        return respDTOS;
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void bankNotic(KafkaDechTradeResultMsg iMessage) {
        if (FundAccountSubType.isBusinessAccount(iMessage.getAccountSubType()) &&
                FundAccountModelType.isRecharge(iMessage.getAccountModel())) {
            AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(iMessage.getAccountFlowId());
            if (Objects.isNull(acctBusinessDebitFlow) || FundAcctSyncBankStatus.hasSyncNew(acctBusinessDebitFlow.getSyncBankStatus())) {
                return;
            }
            if (AccountStatusEnum.isSuccess(iMessage.getTxnSt())) {
                AcctBusinessDebitFlow updateFlow = new AcctBusinessDebitFlow();
                updateFlow.setId(acctBusinessDebitFlow.getId());
                updateFlow.setBankTransNo(iMessage.getSysOrdNo());
                updateFlow.setSyncBankAmount(Objects.isNull(iMessage.getOperationAmount()) ? BigDecimal.ZERO : iMessage.getOperationAmount());
                updateFlow.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
                updateFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
                updateFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
                BankTradeReceiptQryReqDto bankTradeReceiptQryReqDto = new BankTradeReceiptQryReqDto();
                bankTradeReceiptQryReqDto.setBankTransNo(iMessage.getSysOrdNo());
                bankTradeReceiptQryReqDto.setBankName(acctBusinessDebitFlow.getBankName());
                bankTradeReceiptQryReqDto.setCompanyAccountId(acctBusinessDebitFlow.getBankAcctId());
                bankTradeReceiptQryReqDto.setTradeTime(new Date());
                bankTradeReceiptQryReqDto.setTxnType(BankTradeType.getBankTypeByCode(BankTradeType.RECHARGE.getCode(), acctBusinessDebitFlow.getBankName()));
                bankTradeReceiptQryReqDto.setFbOrderId(acctBusinessDebitFlow.getCashierTxnId());
                try {
                    // 中信暂时下掉实时获取电子回单
                    if(!BankNameEnum.isCitic(acctBusinessDebitFlow.getBankName())) {
                        BankTradeReceiptQryRespDto bankTradeReceiptQryRespDto = iBankSearchService.queryTxnReceipt(bankTradeReceiptQryReqDto);
                        if (Objects.nonNull(bankTradeReceiptQryRespDto) && !com.fenbeitong.finhub.common.utils.StringUtils.isBlank(bankTradeReceiptQryRespDto.getDownloadUrl())) {
                            updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_SUCE.getKey());
                            updateFlow.setCostImageUrl(bankTradeReceiptQryRespDto.getDownloadUrl());
                        } else {
                            if (BankNameEnum.isSpa(acctBusinessDebitFlow.getBankName())) {
                                updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_NON.getKey());
                            } else {
                                updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_FAILE.getKey());
                            }
                        }
                    }
                } catch (Exception ex) {
                    FinhubLogger.error("获取电子回单失败：参数：{}", bankTradeReceiptQryReqDto);
                }

                FinhubLogger.info("【kafka消息银行上账结果通知退款，消息消费完成{}", JsonUtils.toJson(updateFlow));
                int upateFlow = acctBusinessDebitFlowService.updateByIdSelective(updateFlow);
                if (upateFlow != 1) {
                    FinhubLogger.error("【商务充值账户，上账状态更新失败】data：{}", JsonUtils.toJson(updateFlow));
                    dingDingMsgService.sendMsg("【商务充值账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
                    throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
                }

                //判断是消费还是退款
                FundPlatAcctOptType fundPlatAcctOptType = null;
                FundAcctCreditOptType fundAcctCreditOptType = FundAcctCreditOptType.getEnum(acctBusinessDebitFlow.getOperationType());
                if(Objects.equals(fundAcctCreditOptType,FundAcctCreditOptType.PUBLIC_CONSUME)){
                    fundPlatAcctOptType = FundPlatAcctOptType.PUBLIC_CONSUME;
                }else if(Objects.equals(fundAcctCreditOptType,FundAcctCreditOptType.PUBLIC_CONSUME_REFUND)){
                    fundPlatAcctOptType = FundPlatAcctOptType.PUBLIC_CONSUME_REFUND;
                }else {
                    throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
                }
                //收款账户上账状态更新
                updateReceiptBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, acctBusinessDebitFlow.getBizNo(),fundPlatAcctOptType,updateFlow.getCostImageUrl());
                /**
                 * 消费消息，不发送对账消息（方案变更，发消息）
                 * QX 2021-12-13 FBT-9264
                 */
                //上账发送消息--自动对账
                KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
                BeanUtils.copyProperties(acctBusinessDebitFlow, kafkaAutoAcctCheckingMsg);
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        //上账发送消息--自动对账
                        autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
                    }
                });
            }
            return;
        }
        if (FundAccountSubType.isIndividualAccount(iMessage.getAccountSubType())) {

            FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByFreezenFlowId(iMessage.getAccountFlowId());
            if (Objects.isNull(fundFreezenFlow) || FundAcctSyncBankStatus.hasSyncNew(fundFreezenFlow.getSyncBankStatus())) {
                return;
            }
            if (FreezenChangeType.isFreezing(fundFreezenFlow.getOperationType())){
                if (FundAccountModelType.isRecharge(iMessage.getAccountModel())){
                    syncBankDebit(iMessage, fundFreezenFlow);
                }
            } else{
                asyncBank(iMessage, fundFreezenFlow,iMessage.getAccountModel());
            }
        }

        // 报销子账户
        if (FundAccountSubType.isReimbursement(iMessage.getAccountSubType())) {
            AcctReimbursementFlow acctReimbursementFlow = acctReimbursementFlowService.queryAccountSubFlowByFlowId(iMessage.getAccountFlowId());
            if (Objects.isNull(acctReimbursementFlow) || FundAcctSyncBankStatus.hasSyncNew(acctReimbursementFlow.getSyncBankStatus())) {
                return;
            }
            if (AccountStatusEnum.isSuccess(iMessage.getTxnSt())) {
                AcctReimbursementFlow updateFlow = new AcctReimbursementFlow();
                updateFlow.setId(acctReimbursementFlow.getId());
                updateFlow.setBankTransNo(iMessage.getSysOrdNo());
                updateFlow.setSyncBankAmount(Objects.isNull(iMessage.getOperationAmount()) ? BigDecimal.ZERO : iMessage.getOperationAmount());
                updateFlow.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
                updateFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
                updateFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
                BankTradeReceiptQryReqDto bankTradeReceiptQryReqDto = new BankTradeReceiptQryReqDto();
                bankTradeReceiptQryReqDto.setBankTransNo(iMessage.getSysOrdNo());
                bankTradeReceiptQryReqDto.setBankName(acctReimbursementFlow.getBankName());
                bankTradeReceiptQryReqDto.setCompanyAccountId(acctReimbursementFlow.getBankAcctId());
                bankTradeReceiptQryReqDto.setTradeTime(new Date());
                bankTradeReceiptQryReqDto.setTxnType(BankTradeType.getBankTypeByCode(BankTradeType.RECHARGE.getCode(), acctReimbursementFlow.getBankName()));
                bankTradeReceiptQryReqDto.setFbOrderId(acctReimbursementFlow.getCashierTxnId());
                try {
                    // 中信暂时下掉实时获取电子回单
                    if (!BankNameEnum.isCitic(acctReimbursementFlow.getBankName())) {
                        BankTradeReceiptQryRespDto bankTradeReceiptQryRespDto = iBankSearchService.queryTxnReceipt(bankTradeReceiptQryReqDto);
                        if (Objects.nonNull(bankTradeReceiptQryRespDto) && !com.fenbeitong.finhub.common.utils.StringUtils.isBlank(bankTradeReceiptQryRespDto.getDownloadUrl())) {
                            updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_SUCE.getKey());
                            updateFlow.setCostImageUrl(bankTradeReceiptQryRespDto.getDownloadUrl());
                        } else {
                            updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_FAILE.getKey());
                        }
                    }
                } catch (Exception ex) {
                    FinhubLogger.error("获取电子回单失败：参数：{}", bankTradeReceiptQryReqDto);
                }

                FinhubLogger.info("【kafka消息银行上账结果通知退款，消息消费完成{}", JsonUtils.toJson(updateFlow));
                int updatedCount = acctReimbursementFlowService.updateByIdSelective(updateFlow);
                if (updatedCount != 1) {
                    FinhubLogger.error("【报销子账户，上账状态更新失败】data：{}", JsonUtils.toJson(updateFlow));
                    dingDingMsgService.sendMsg("【报销子账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
                    throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
                }

                // 判断是消费/退款
                FundPlatAcctOptType fundPlatAcctOptType = null;
                FundAcctCreditOptType fundAcctCreditOptType = FundAcctCreditOptType.getEnum(acctReimbursementFlow.getOperationType());
                if (Objects.equals(fundAcctCreditOptType, FundAcctCreditOptType.PUBLIC_CONSUME)) {
                    fundPlatAcctOptType = FundPlatAcctOptType.PUBLIC_CONSUME;
                } else if (Objects.equals(fundAcctCreditOptType, FundAcctCreditOptType.PUBLIC_CONSUME_REFUND)) {
                    fundPlatAcctOptType = FundPlatAcctOptType.PUBLIC_CONSUME_REFUND;
                } else {
                    throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
                }
                // 发送对账消息
                // 报销子账户 主要作用 更新上账流水号
                KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
                BeanUtils.copyProperties(acctReimbursementFlow, kafkaAutoAcctCheckingMsg);
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
                    }
                });
            }
            return;

        }

        // 备用金账户
        if (FundAccountSubType.isComCardAccount(iMessage.getAccountSubType())) {
            AcctCompanyCardFlow acctCompanyCardFlow = acctCompanyCardFlowService.queryByBizNo(iMessage.getTxnId());
            if (Objects.isNull(acctCompanyCardFlow) || FundAcctSyncBankStatus.hasSyncNew(acctCompanyCardFlow.getSyncBankStatus())) {
                return;
            }
            if (AccountStatusEnum.isSuccess(iMessage.getTxnSt())) {
                AcctCompanyCardFlow updateFlow = new AcctCompanyCardFlow();
                updateFlow.setId(acctCompanyCardFlow.getId());
                updateFlow.setBankTransNo(iMessage.getSysOrdNo());
                updateFlow.setSyncBankAmount(Objects.isNull(iMessage.getOperationAmount()) ? BigDecimal.ZERO : iMessage.getOperationAmount());
                updateFlow.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
                updateFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
                updateFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
                BankTradeReceiptQryReqDto bankTradeReceiptQryReqDto = new BankTradeReceiptQryReqDto();
                bankTradeReceiptQryReqDto.setBankTransNo(iMessage.getSysOrdNo());
                bankTradeReceiptQryReqDto.setBankName(acctCompanyCardFlow.getBankName());
                bankTradeReceiptQryReqDto.setCompanyAccountId(acctCompanyCardFlow.getBankAcctId());
                bankTradeReceiptQryReqDto.setTradeTime(new Date());
                bankTradeReceiptQryReqDto.setTxnType(BankTradeType.getBankTypeByCode(BankTradeType.RECHARGE.getCode(), acctCompanyCardFlow.getBankName()));
                bankTradeReceiptQryReqDto.setFbOrderId(acctCompanyCardFlow.getBizNo());
                try {
                    // 中信暂时下掉实时获取电子回单
                    if (!BankNameEnum.isCitic(acctCompanyCardFlow.getBankName())) {
                        BankTradeReceiptQryRespDto bankTradeReceiptQryRespDto = iBankSearchService.queryTxnReceipt(bankTradeReceiptQryReqDto);
                        if (Objects.nonNull(bankTradeReceiptQryRespDto) && !com.fenbeitong.finhub.common.utils.StringUtils.isBlank(bankTradeReceiptQryRespDto.getDownloadUrl())) {
                            updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_SUCE.getKey());
                            updateFlow.setCostImageUrl(bankTradeReceiptQryRespDto.getDownloadUrl());
                        } else {
                            updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_FAILE.getKey());
                        }
                    }
                } catch (Exception ex) {
                    FinhubLogger.error("获取电子回单失败：参数：{}", bankTradeReceiptQryReqDto);
                }
                FinhubLogger.info("【kafka消息银行上账结果通知退款，消息消费完成{}", JsonUtils.toJson(updateFlow));
                int updatedCount = acctCompanyCardFlowService.updateByIdSelective(updateFlow);
                if (updatedCount != 1) {
                    FinhubLogger.error("【备用金子账户，上账状态更新失败】data：{}", JsonUtils.toJson(updateFlow));
                    dingDingMsgService.sendMsg("【备用金子账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
                    throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
                }
                // 发送对账消息
                // 备用金子账户 主要作用 更新上账流水号
                KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
                BeanUtils.copyProperties(acctCompanyCardFlow, kafkaAutoAcctCheckingMsg);
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
                    }
                });
            } else {
                // 平安虚拟卡返还限额
                applyCreditFailReturnAcctLimit(iMessage.getBankName(), iMessage.getTxnId());
            }
        }
        if (iMessage.getAccountSubType() == 51 && iMessage.getBankName().equals(BankNameEnum.SPABANK.getCode())){
            FinhubLogger.info("【kafka消息银行上账结果通知平安错花结果 = {}", JsonUtils.toJson(iMessage));
            RepaymentNocReqDTO repaymentNocReqDTO = new RepaymentNocReqDTO();
            repaymentNocReqDTO.setRepaymentOrderId(iMessage.getAccountFlowId());
            //平安错花还款   首先查询acct-person  并且更新单据状态  然后拿到fb_orderId 更新支付单状态信息
            if (AccountStatusEnum.isSuccess(iMessage.getTxnSt())){
                repaymentNocReqDTO.setPayStatus(3);
            }else {
                repaymentNocReqDTO.setPayStatus(4);
            }
            String fbOrderId = iBankUserCardTradeService.updateRepaymentStatus(repaymentNocReqDTO);
            FinhubLogger.info("【kafka消息银行  更新平安acct还款结果 req = {} , res  = {}",JSON.toJSONString(repaymentNocReqDTO),fbOrderId);
        }
    }


    //===============================================private method=====================

    /**
     * 申请额度失败返还限额
     */
    private void applyCreditFailReturnAcctLimit(String bankName, String bizNo) {
        if (!BankNameEnum.isSpa(bankName)) {
            return;
        }
        List<BankAcctLimitFlow> bankAcctLimitFlows = bankAcctLimitService.getBankAcctLimitFlowByBizNo(bizNo);
        if (CollectionUtils.isEmpty(bankAcctLimitFlows)) {
            return;
        }

        for (BankAcctLimitFlow bankAcctLimitFlow : bankAcctLimitFlows) {
            if (bankAcctLimitFlow.getOperationAmount().compareTo(BigDecimal.ZERO) < 0) {
                continue;
            }
            BankAcctLimitReqVO reqVO = new BankAcctLimitReqVO();
            reqVO.setBizNo(bankAcctLimitFlow.getBizNo());
            reqVO.setBankAccountNo(bankAcctLimitFlow.getBankAccountNo());
            reqVO.setTradeAmount(BigDecimal.ZERO.subtract(bankAcctLimitFlow.getOperationAmount()));
            reqVO.setTradeType(bankAcctLimitFlow.getLimitTradeType());
            bankAcctLimitService.returnBankAcctLimit(reqVO);
        }
    }

    private void createIndiAccountByFbt(AcctCreateSubDTO acctCreateSubDTO, CompanyPlatformAccountConvertDTO comPlatAccListDe, CompanyPlatformAccountConvertDTO comPlatAccListCr) {
        //创建个人账户
        if (FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel())) {
            AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
            //激活时更新网关信息
            AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
            BeanUtils.copyProperties(acctIndividualDebit, reqDTO);
            reqDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            reqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
            reqDTO.setBankAccountNo(acctIndividualDebit.getBankAccountNo());
            acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);
            //对手
            if (Objects.nonNull(comPlatAccListDe)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListDe.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setActiveStatus(otherActiveStatus);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            uAcctIndividualCreditService.establishAccount(acctCreateSubDTO);
        } else if (FundAccountModelType.isCredit(acctCreateSubDTO.getAccountModel())) {
            AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.establishAccount(acctCreateSubDTO);
            //激活时更新网关信息
            AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
            BeanUtils.copyProperties(acctIndividualCredit, reqDTO);
            reqDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            reqDTO.setAccountModel(FundAccountModelType.CREDIT.getKey());
            reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
            reqDTO.setBankAccountNo(acctIndividualCredit.getBankAccountNo());
            acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);

            if (Objects.nonNull(comPlatAccListCr)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListCr.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setActiveStatus(otherActiveStatus);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
        }
    }

    private void createIndiAccountByFbtAuthBefore(AcctCreateSubDTO acctCreateSubDTO, CompanyPlatformAccountConvertDTO comPlatAccListDe, CompanyPlatformAccountConvertDTO comPlatAccListCr) {
        //创建个人账户
        if (FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel())) {
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()){
                acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            }
            AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
            //激活时更新网关信息
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()) {
                AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
                BeanUtils.copyProperties(acctIndividualDebit, reqDTO);
                reqDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                reqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
                reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
                reqDTO.setBankAccountNo(acctIndividualDebit.getBankAccountNo());
                acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);
            }
            //对手
            if (Objects.nonNull(comPlatAccListDe)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListDe.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
            uAcctIndividualCreditService.establishAccount(acctCreateSubDTO);
        } else if (FundAccountModelType.isCredit(acctCreateSubDTO.getAccountModel())) {
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()){
                acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            }
            AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.establishAccount(acctCreateSubDTO);
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()) {
                //激活时更新网关信息
                AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
                BeanUtils.copyProperties(acctIndividualCredit, reqDTO);
                reqDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                reqDTO.setAccountModel(FundAccountModelType.CREDIT.getKey());
                reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
                reqDTO.setBankAccountNo(acctIndividualCredit.getBankAccountNo());
                acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);
            }

            if (Objects.nonNull(comPlatAccListCr)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListCr.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()){
                acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            }else {
                acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
        }
    }

    private void createBussAccountByFbt(AcctCreateSubDTO acctCreateSubDTO, CompanyPlatformAccountConvertDTO comPlatAccListDe, CompanyPlatformAccountConvertDTO comPlatAccListCr) {
        //创建商务账户
        if (FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel())) {
            //创建激活的账户
            AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
            //激活时更新网关信息
            AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
            BeanUtils.copyProperties(acctBusinessDebit, reqDTO);
            reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            reqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
            reqDTO.setBankAccountNo(acctBusinessDebit.getBankAccountNo());
            acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);

            //创建对手账户
            if (Objects.nonNull(comPlatAccListDe)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListDe.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setActiveStatus(otherActiveStatus);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
        } else if (FundAccountModelType.isCredit(acctCreateSubDTO.getAccountModel())) {
            AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
            //激活时更新网关信息
            AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
            BeanUtils.copyProperties(acctBusinessCredit, reqDTO);
            reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            reqDTO.setAccountModel(FundAccountModelType.CREDIT.getKey());
            reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
            reqDTO.setBankAccountNo(acctBusinessCredit.getBankAccountNo());
            acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);

            if (Objects.nonNull(comPlatAccListCr)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListCr.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setActiveStatus(otherActiveStatus);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
        }
    }

    private void createBussAccountByFbtAuthBefore(AcctCreateSubDTO acctCreateSubDTO, CompanyPlatformAccountConvertDTO comPlatAccListDe, CompanyPlatformAccountConvertDTO comPlatAccListCr) {
        //创建商务账户
        if (FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel())) {
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()){
                acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            }
            //创建激活的账户
            AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
            //激活时更新网关信息
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()) {
                AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
                BeanUtils.copyProperties(acctBusinessDebit, reqDTO);
                reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                reqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
                reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
                reqDTO.setBankAccountNo(acctBusinessDebit.getBankAccountNo());
                acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);
            }

            //创建对手账户
            if (Objects.nonNull(comPlatAccListDe)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListDe.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()){
                acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            }else {
                acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
            }
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
        } else if (FundAccountModelType.isCredit(acctCreateSubDTO.getAccountModel())) {
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()){
                acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            }
            AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
            //激活时更新网关信息
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()) {
                AcctComGwUpdateReqDTO reqDTO = AcctComGwUpdateReqDTO.builder().build();
                BeanUtils.copyProperties(acctBusinessCredit, reqDTO);
                reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                reqDTO.setAccountModel(FundAccountModelType.CREDIT.getKey());
                reqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
                reqDTO.setBankAccountNo(acctBusinessCredit.getBankAccountNo());
                acctCompanyGatewayService.updateActGwByComIdAndSubType(reqDTO);
            }

            if (Objects.nonNull(comPlatAccListCr)) {
                acctCreateSubDTO.setAccountStatus(comPlatAccListCr.getBusinessConsume());
            } else {
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
            }
            if(Objects.nonNull(comPlatAccListDe.getSource()) && EffectiveAccountSourceEnum.NORMAL.getKey() ==  comPlatAccListDe.getSource()){
                acctCreateSubDTO.setActiveStatus(ACTIVATE.getStatus());
            }else {
                acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
            }
            Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
            Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
            acctCreateSubDTO.setAccountModel(otherAccountModel);
            acctCreateSubDTO.setShowStatus(otherShowStatus);
            uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
        }
    }

    @Override
    public AcctCompanyUpgradedRespDTO upgradeAcctCompany(AcctCompanyUpgradeReqDTO acctCompanyUpgradeReqDTO) {
        AcctCompanyUpgradedRespDTO acctCompanyUpgradedRespDTO = new AcctCompanyUpgradedRespDTO();
        acctCompanyUpgradedRespDTO.setCompanyId(acctCompanyUpgradeReqDTO.getCompanyId());
        AcctComUpdate4ByComIdReqDTO reqDTO = new AcctComUpdate4ByComIdReqDTO();
        reqDTO.setCompanyId(acctCompanyUpgradeReqDTO.getCompanyId());
        AcctComUpdate4RespDTO acctComUpdate4RespDTO = uAcctComUpdateService.findActUpdate4ByComId(reqDTO);
        if (acctComUpdate4RespDTO != null && FundAcctUpdate4Enum.isUpdateOn(acctComUpdate4RespDTO.getUpdateStatus())) {
            acctCompanyUpgradedRespDTO.setUpdateStatus(acctComUpdate4RespDTO.getUpdateStatus());
            upgradeAcctPublic(acctCompanyUpgradeReqDTO.getCompanyId());
            upgradeCompanyCard(acctCompanyUpgradeReqDTO.getCompanyId());
            createAcctCompanySwitch(acctCompanyUpgradeReqDTO.getCompanyId());
            iCompanyService.createCompanyPlatformAccountConfig(acctCompanyUpgradeReqDTO.getCompanyId());
            //改虚拟卡的生效帐户。需要通知UC改银行卡
            CompletableFuture.runAsync(() -> {
                try {
                    List<CompanyVirtualCardDTO> cardDTOList = new ArrayList<>();
                    CompanyVirtualCardDTO virtualCardDTO = new CompanyVirtualCardDTO();
                    virtualCardDTO.setCardIssuerCode(BankNameEnum.CGB.getCode());
                    virtualCardDTO.setCardIssuerName(BankNameEnum.getBankEnum(BankNameEnum.CGB.getCode()).getName());
                    cardDTOList.add(virtualCardDTO);
                    FinhubLogger.info("改虚拟卡的生效帐户,通知UC改银行卡companyId={}, cardDTOList={}", acctCompanyUpgradeReqDTO.getCompanyId(),JsonUtils.toJson(cardDTOList));
                    iCompanyService.updateCompanyVirtualCardIssuerV2(acctCompanyUpgradeReqDTO.getCompanyId(), cardDTOList);
                    //iCompanyService.updateCompanyVirtualCardIssuer(acctCompanyUpgradeReqDTO.getCompanyId(), BankNameEnum.CGB.getCode(), BankNameEnum.getBankEnum(BankNameEnum.CGB.getCode()).getName());
                } catch (Exception e) {
                    dingDingMsgService.sendMsg("改虚拟卡的生效帐户。需要通知UC改银行卡失败：{}" + acctCompanyUpgradeReqDTO.getCompanyId());
                    FinhubLogger.error("改虚拟卡的生效帐户。需要通知UC改银行卡失败{}==={}", JsonUtils.toJson(acctCompanyUpgradeReqDTO), e);
                }
            }, asyncExecutor);
            acctCompanyUpgradedRespDTO.setUpgradedStatus(Boolean.TRUE);
            FinhubLogger.info("企业账户升级完成,本次升级:参数={}", JsonUtils.toJson(acctCompanyUpgradeReqDTO));
        } else {
            acctCompanyUpgradedRespDTO.setUpgradedStatus(Boolean.FALSE);
            acctCompanyUpgradedRespDTO.setUpdateStatus(FundAcctUpdate4Enum.UPDATE_OFF.getStatus());
        }
        return acctCompanyUpgradedRespDTO;
    }
    
    @Override
    public AcctFlowExportRespDTO exportCompanyBillExcel(BillFlowPageQuery param) {
        // 创建下载任务，然后异步生成Excel并上传然后更新任务状态、结果
        String fileName = String.valueOf(System.currentTimeMillis());
        ExportElectronicTaskDTO task = ExportElectronicTaskDTO.builder()
            .taskSrc(param.getTaskSrc())
            .taskName(fileName)
            .taskCategory(9999)
            .taskCategoryName("流水账单导出")
            .totalSize(1)
            .fileName(fileName)
            .createId(param.getUserId())
            .createName(param.getUserName())
            .build();
        
        String json = JSON.toJSONString(task);
        String taskId = createExportTask(json);
        AcctFlowExportRespDTO resp = new AcctFlowExportRespDTO(taskId, param.getBankName(), "下载到本地");

        CompletableFuture.runAsync(() -> {
        	PayContext.setUserId(Optional.ofNullable(param.getUserId()).orElse("system"));
        	generateBillExcelAndUploadIt(param, taskId, fileName);
        	PayContext.clearConext();
        }, asyncExecutor);
        
        return resp;
    }
    
    private String createExportTask(String json) {
        FinhubLogger.info("【调用任务中心创建任务】请求参数：{}", json);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/outer/task", json);
        FinhubLogger.info("【调用任务中心创建任务】返回结果：{}", postBody);
        String taskId = JSON.parseObject(postBody).getJSONObject("data").getString("taskId");
        return taskId;
    }
    
    /**
     * 
     * @param param
     * @param taskId
     * @param fileName
     */
    private void generateBillExcelAndUploadIt(BillFlowPageQuery param, String taskId, String fileName) {
        int taskStatus = 10;
        String url = StringUtils.EMPTY;
        try {
            List<AcctFlowBaseDTO> billData = accountBillFlowService.queryBillFlowData4Export(param);
            SXSSFWorkbook workbook = ExportBillExcelUtil.createWorkbook(billData);
            
            url = uploadExcel2OSS(workbook, fileName);
            if (StringUtils.isBlank(url)) {
                taskStatus = 50;
            }
        } catch (Exception e) {
            taskStatus = 50;
            FinhubLogger.error("【导出流水账单】异常", e);
        } finally {
            noticeTaskProcess(taskStatus, fileName, taskId, url);
        }
    }
    
    /**
     * 
     * @param workbook
     * @param fileName
     * @return
     * @throws FileNotFoundException
     * @throws IOException
     */
    private String uploadExcel2OSS(SXSSFWorkbook workbook, String fileName) throws IOException {
        if (Objects.isNull(workbook)) {
            return StringUtils.EMPTY;
        }
        
        String uri = buildExcelUri(fileName);
        File file = new File(FileUtils.getTempDirectoryPath(), fileName + ".xlsx");
        try(FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            workbook.write(fileOutputStream);
            return uploadFile2Oss(uri, file);
        } finally {
            workbook.dispose();
            FileUtils.deleteQuietly(file);
        }
    }
    
    private String buildExcelUri(String fileName) {
        return getTimeAtNow().concat(fileName).concat(".xlsx");
    }
    
    /**
     * 批量导出电子回单
     *
     * @param
     * @return
     */
    @Override
    public AcctFlowExportRespDTO exportElectronicReceipt(AcctUserInfoDTO acctUserInfoDTO, List<AcctFlowRespDTO> flow, AcctOptFlowReqDTO req) {
        AcctFlowExportRespDTO acctFlowExportRespDTO = new AcctFlowExportRespDTO();
        String url = "";
        StringBuilder printVerifyCodeBuilder = new StringBuilder();
        try {
            // 处理中信 直接返回url 跳转到中信网站下载
            if (BankNameEnum.isCitic(req.getBankName())) {
                for (AcctFlowRespDTO acctFlowRespDTO : flow) {
                    if (!StringUtils.isBlank(acctFlowRespDTO.getCostImageUrl())
                            && (Objects.nonNull(acctFlowRespDTO.getCostImageStatus())
                            && FundAcctCostImageStatus.isCostImageSuce(acctFlowRespDTO.getCostImageStatus()))) {
                        if (BankNameEnum.isCitic(acctFlowRespDTO.getBankName())) {
                            url = acctFlowRespDTO.getCostImageUrl();
                            printVerifyCodeBuilder.append(acctFlowRespDTO.getCostImageUrl().split("printVerifyCode=")[1]);
                            printVerifyCodeBuilder.append(",");
                        }
                    }
                }
                // 电子回单url不存在 返回可下载电子回单为空
                if (StringUtils.isBlank(printVerifyCodeBuilder.toString())) {
                    throw new FinhubException(GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getCode(),
                            GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getType(), GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getMsg());
                }
                if (BankNameEnum.isCitic(req.getBankName())) {
                    String citcUrl = printVerifyCodeBuilder.toString();
                    citcUrl = url.split("printVerifyCode=")[0] + "printVerifyCode=" + citcUrl.substring(0, citcUrl.length() - 1);
                    acctFlowExportRespDTO.setBankName(BankNameEnum.CITIC.getCode());
                    acctFlowExportRespDTO.setCitcUrl(citcUrl);
                    return acctFlowExportRespDTO;
                }
            }
            // 下载数量限制 oss最多支持上传5G的打包数据
            if (flow.size() > DOWNLOAD_RECEIPT_COUNT_LIMIT) {
                dingDingMsgService.sendMsg("下载电子回单失败 数量超过"+ DOWNLOAD_RECEIPT_COUNT_LIMIT);
                throw new FinhubException(DOWNLOAD_RECEIPT_COUNT_OVER_LIMIT.getCode(), DOWNLOAD_RECEIPT_COUNT_OVER_LIMIT.getType(), DOWNLOAD_RECEIPT_COUNT_OVER_LIMIT.getMsg());
            }
            // 处理众邦和平安 从oss下载电子回单
            String taskCategoryName = "导出电子回单";
            final String fileNam = getExportFileName(flow);
            // 创建下载任务
            ExportElectronicTaskDTO exportDTO = new ExportElectronicTaskDTO(
                    req.getTaskSrc(), fileNam, RECEIPT_TASK_CATEGORY, taskCategoryName, flow.size(), fileNam,
                    acctUserInfoDTO.getId(), acctUserInfoDTO.getName());
            String jsonString = JSON.toJSONString(exportDTO);
            FinhubLogger.info("【调用任务中心创建任务】请求参数：{}", jsonString);
            String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/outer/task", jsonString);
            FinhubLogger.info("【调用任务中心创建任务】返回参数：{}", postBody);
            String taskId = JSON.parseObject(postBody).getJSONObject("data").getString("taskId");
            acctFlowExportRespDTO.setId(taskId);
            acctFlowExportRespDTO.setBankName(req.getBankName());
            acctFlowExportRespDTO.setButtonText("下载到本地");
            // 打印操作 且数量不大于100 展示打印按钮
            if (MergePdfEnum.merge.getKey().equals(req.getMergePdf()) && flow.size() <= MERGER_PDF_LIMIT) {
                acctFlowExportRespDTO.setButtonText("打印");
            }
            // 异步下载
            CompletableFuture.runAsync(() -> {
            	PayContext.setUserId(Optional.ofNullable(acctUserInfoDTO.getId()).orElse("system"));
                downLoadReceipt(flow, fileNam, taskId, req.getAccountSubType(), req.getMergePdf());
                PayContext.clearConext();
            }, asyncExecutor);
            return acctFlowExportRespDTO;
        } catch (FinhubException e) {
            throw e;
        } catch (Exception e) {
            dingDingMsgService.sendMsg("下载电子回单失败:" + JsonUtils.toJson(flow));
            FinhubLogger.error("下载电子回单失败:", e);
        }
        return null;
    }

    @Override
    public AcctFlowExportRespDTO exportLfElectronicReceipt(AcctUserInfoDTO acctUserInfoDTO, AcctOptFlowReqDTO req, LfTradeReceiptRespDTO respDTO) {
        AcctFlowExportRespDTO acctFlowExportRespDTO = new AcctFlowExportRespDTO();
        
        try {
            String taskCategoryName = "导出电子回单";
            final String fileNam = getLfExportFileName(req);
            // 创建下载任务
            ExportElectronicTaskDTO exportDTO = new ExportElectronicTaskDTO(
                    req.getTaskSrc(), fileNam, RECEIPT_TASK_CATEGORY, taskCategoryName, respDTO.getDownloadUrl().size(), fileNam,
                    acctUserInfoDTO.getId(), acctUserInfoDTO.getName());
            String jsonString = JSON.toJSONString(exportDTO);
            FinhubLogger.info("【调用任务中心创建任务】请求参数：{}", jsonString);
            String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/outer/task", jsonString);
            FinhubLogger.info("【调用任务中心创建任务】返回参数：{}", postBody);
            String taskId = JSON.parseObject(postBody).getJSONObject("data").getString("taskId");
            acctFlowExportRespDTO.setId(taskId);
            acctFlowExportRespDTO.setBankName(req.getBankName());
            acctFlowExportRespDTO.setButtonText("下载到本地");
            // 打印操作 且数量不大于100 展示打印按钮
            if (MergePdfEnum.merge.getKey().equals(req.getMergePdf()) && respDTO.getDownloadUrl().size() <= MERGER_PDF_LIMIT) {
                acctFlowExportRespDTO.setButtonText("打印");
            }
            // 异步下载
            CompletableFuture.runAsync(() -> {
            	PayContext.setUserId(Optional.ofNullable(acctUserInfoDTO.getId()).orElse("system"));
                downLoadReceipt(respDTO.getDownloadUrl(), fileNam, taskId, req.getMergePdf(), req.getBankAccountNo());
                PayContext.clearConext();
            }, asyncExecutor);
            return acctFlowExportRespDTO;
        } catch (FinhubException e) {
            throw e;
        } catch (Exception e) {
            dingDingMsgService.sendMsg("下载电子回单失败:" + JsonUtils.toJson(req));
            FinhubLogger.error("下载电子回单失败:", e);
        }
        return null;
    }

    private void downLoadReceipt( List<String> downloadUrl, String fileNam, String taskId, String mergePdf, String bankAccountNo) {
        // [默认1]执行状态:1 执行中
        int status = 1;
        String ossUrl = "";
        List<File> localFileList = new ArrayList<>();
        try {
            for (String url : downloadUrl) {
                // 电子回单状态扩展 流水状态扩展 下载状态也要扩展
                if (!StringUtils.isBlank(url)) {
                    // oss图片地址
                    String imageUrl =url.split("com/")[1];
                    // 本地文件路径名称
                    String pathName = genFileName(bankAccountNo);
                    // 从oss下载到本地文件
                    downOss2Local(imageUrl, pathName);
                    // 保存文件句柄
                    localFileList.add(new File(pathName));
                }
            }
            // 逐条上传
            if (MergePdfEnum.not_merge.getKey().equals(mergePdf)) {
                ossUrl = itemPdf(localFileList, fileNam);
            } else {
                // 合并上传
                ossUrl = mergePdf(localFileList, fileNam);
            }
            // 下载状态
            if (Objects.isNull(ossUrl)) {
                // 50 执行失败
                status = 50;
            } else {
                // 10 执行成功
                status = 10;
            }
        } catch (Exception e) {
            // 50 执行失败 出现异常
            status = 50;
            FinhubLogger.error("电子回单下载失败 taskId:" + taskId, e);
        } finally {
            // 通知下载结果
            noticeTaskProcess(status, fileNam, taskId, ossUrl);
            // 删除文件
            if (CollectionUtils.isNotEmpty(localFileList)) {
                for (File srcFile : localFileList) {
                    srcFile.delete();
                }
            }
        }
    }

    private void downLoadReceipt( List<AcctFlowRespDTO> flow, String fileNam, String taskId, Integer accountSubType, String mergePdf) {
        // [默认1]执行状态:1 执行中
        int status = 1;
        String ossUrl = "";
        List<File> localFileList = new ArrayList<>();
        try {
            for (AcctFlowRespDTO acctFlowRespDTO : flow) {
                // 电子回单状态扩展 流水状态扩展 下载状态也要扩展
                if (!StringUtils.isBlank(acctFlowRespDTO.getCostImageUrl())
                        && (Objects.nonNull(acctFlowRespDTO.getCostImageStatus())
                        && (CostImageStatusMapping.canDownLoad(acctFlowRespDTO.getCostImageStatus())))) {
                    // oss图片地址
                    String imageUrl = acctFlowRespDTO.getCostImageUrl().split("com/")[1];
                    // 本地文件路径名称
                    String pathName = genFileName(acctFlowRespDTO.getAccountFlowId());
                    // 从oss下载到本地文件
                    downOss2Local(imageUrl, pathName);
                    // 保存文件句柄
                    localFileList.add(new File(pathName));
                }
            }
            // 逐条上传
            if (MergePdfEnum.not_merge.getKey().equals(mergePdf)) {
                ossUrl = itemPdf(localFileList, fileNam);
            } else {
                // 合并上传
                ossUrl = mergePdf(localFileList, fileNam);
            }
            // 下载状态
            if (StringUtils.isBlank(ossUrl)) {
                // 50 执行失败
                status = 50;
            } else {
                // 10 执行成功
                status = 10;
            }
        } catch (Exception e) {
            // 50 执行失败 出现异常
            status = 50;
            FinhubLogger.error("电子回单下载失败 taskId:" + taskId, e);
        } finally {
            // 通知下载结果
            noticeTaskProcess(status, fileNam, taskId, ossUrl);
            // 删除文件
            if (CollectionUtils.isNotEmpty(localFileList)) {
                for (File srcFile : localFileList) {
                    srcFile.delete();
                }
            }
            //更新为已经下载/已打印
            if(status == 10){
                CompletableFuture.runAsync(() -> updateBatchFlowOfCostImageStatus(flow, accountSubType, mergePdf), asyncExecutor);
            }
        }
    }

    private String itemPdf(List<File> localFileList, String fileNam) {
        FileInputStream stream = fileStreamService.toZip(localFileList);
        if (stream == null) {
            return null;
        }
        // 上传压缩包到oss 提供下载使用
        String ossUri = getTimeAtNow() + fileNam + ".zip";
        return uploadFile2Oss(ossUri, stream);
    }

    private String mergePdf(List<File> localFileList, String fileNam) {
        // 合并文件的本地文件
        String mergeFileName = genFileName("merger_receipt");
        File mergeFile = new File(mergeFileName);
        // 上传到oss 提供下载使用
        String ossUri = getTimeAtNow() + fileNam;
        try {
            PDFMergerUtility mergerUtility = new PDFMergerUtility();
            for (File f : localFileList) {
                try {
                    mergerUtility.addSource(f);
                } catch (FileNotFoundException fnfe) {
                    FinhubLogger.error("合并pdf文件异常", fnfe);
                    throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(), EXCEPTION.getMsg());
                }
            }
            // 合并pdf文件
            mergerUtility.setDestinationFileName(mergeFileName);
            // 合并后的pdf放到临时文件 不要放入主内存
            mergerUtility.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
            // 文件大于100条 需要压缩上传
            if (localFileList.size() > MERGER_PDF_LIMIT) {
                FileInputStream stream = fileStreamService.toZip(Collections.singletonList(mergeFile));
                // 上传压缩合并文件到oss
                return uploadFile2Oss(ossUri + ".zip", stream);
            } else {
                // 上传合并文件到oss
                return uploadFile2Oss(ossUri + ".pdf", mergeFile);
            }
        } catch (Exception e) {
            FinhubLogger.error("合并pdf文件异常", e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(), EXCEPTION.getMsg());
        } finally {
            // 删除文件
            mergeFile.delete();
        }
    }

    /**
     * 批量更新流水为已下载
     * @param flowRespDTOS
     */
    private void updateBatchFlowOfCostImageStatus(List<AcctFlowRespDTO> flowRespDTOS, Integer accountSubType, String mergePdf) {
        FinhubLogger.info("【批量更新流水为已下载】参数打印 flowRespDTOS={} accountSubType={}", JsonUtils.toJson(flowRespDTOS), accountSubType);

        //1:参数校验
        if(CollectionUtils.isEmpty(flowRespDTOS) || Objects.isNull(accountSubType)){
            FinhubLogger.info("【批量更新流水为已下载】没有要处理的数据 flowRespDTOS.size=0 /bankName=null/accountSubType=null");
            return;
        }

        //2:过滤出要处理的数据（众邦全部场景+平安对公付款场景）
        List<AcctFlowRespDTO> flowRespDTOSFilters = flowRespDTOS.stream()
                .filter(flowRespDTO -> BankNameEnum.isZBBank(flowRespDTO.getBankName())
                        || (BankNameEnum.isSpa(flowRespDTO.getBankName()) && Objects.equals(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey(), flowRespDTO.getAccountSubType())))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(flowRespDTOSFilters)){
            FinhubLogger.info("【批量更新流水为已下载】过滤后没有要处理的数据 flowRespDTOSFilters.size=0");
            return;
        }

        //3:更新已下载
        List<String> flowIds = flowRespDTOSFilters.stream().map(AcctFlowRespDTO::getAccountFlowId).collect(Collectors.toList());
        updateBatchFlowByFlowIds(flowIds, accountSubType, mergePdf);
    }

    /**
     * 根据场景类型，批量更新流水
     * @param flowIds
     * @param accountSubType
     */
    private void updateBatchFlowByFlowIds(List<String> flowIds, Integer accountSubType, String mergePdf) {
        FinhubLogger.info("【根据场景批量更新流水】flowIds={} accountSubType={} mergePdf = {}", JsonUtils.toJson(flowIds), accountSubType, mergePdf);

        //1:参数校验
        if(CollectionUtils.isEmpty(flowIds) || Objects.isNull(accountSubType)){
            FinhubLogger.info("【根据场景批量更新流水】flowIds.size=0 accountSubType=null");
            return;
        }

        //2:根据场景更新不同的流水
        switch (FundAccountSubType.getEnum(accountSubType)) {
               //a:账户余额流水更新已下载
            case GENERAL_ACCOUNT:
                accountGeneralFlowService.updateBatchDownLoadFlowByFlowIds(flowIds, mergePdf);
                break;
               //b:商务消费流水更新已下载
            case BUSINESS_ACCOUNT:
                acctBusinessDebitFlowService.updateBatchDownLoadFlowByFlowIds(flowIds, mergePdf);
                break;
                //c:个人消费流水更新已下载
            case INDIVIDUAL_ACCOUNT:
                acctIndividualDebitFlowService.updateBatchDownLoadFlowByFlowIds(flowIds, mergePdf);
                break;
                //d:虚拟卡消费流水更新已下载
            case BANK_VIRTUAL_ACCOUNT:
                acctCompanyCardFlowService.updateBatchDownLoadFlowByFlowIds(flowIds, mergePdf);
                break;
                //e:对公付款流水更新已下载
            case BANK_PUBLIC_ACCOUNT:
                acctPublicFlowManager.updateBatchDownLoadFlowByFlowIds(flowIds, mergePdf);
                break;
            case REIMBURSEMENT_ACCOUNT:
                acctReimbursementFlowService.updateBatchDownLoadFlowByFlowIds(flowIds);
            default :
                FinhubLogger.error("根据场景批量更新流水】accountSubType = {}", accountSubType);
                throw new FinhubException(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getCode(),
                        GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getType(), GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getMsg());
        }

    }

    private String genFileName(String id) {
        return ossUploadTempDir + id + "_" + ObjectId.get().toString() + ".pdf";
    }

    private void downOss2Local(String imageUrl, String pathName) {
        // 从oss下载到本地文件
    	newOssHandler.downloadFileFromOSS(imageUrl, new File(pathName));
    }

    private String uploadFile2Oss(String key, FileInputStream stream) {
        // 上传本地文件到oss
    	return newOssHandler.uploadFileToOss(stream, PayContext.getUserId(), ObjectId.get().toString(), key, OSS_BIZ_CODE);
    }

    private String uploadFile2Oss(String key, File file) {
        // 上传本地文件到oss
    	return newOssHandler.uploadFileToOss(file, PayContext.getUserId(), ObjectId.get().toString(), key, OSS_BIZ_CODE);
    }

    private void noticeTaskProcess(int status, String fileNam, String taskId, String ossUrl) {
        Map<String, Object> updateProgress = new HashMap<>();
        // [默认1]执行状态:1 执行中;10 执行成功;50 执行失败
        updateProgress.put("status", status);
        // 文件名称
        updateProgress.put("fileName", fileNam);
        // [状态10时必填]文件下载链接
        updateProgress.put("downloadUrl", ossUrl);
        String updateProgressJson = JSON.toJSONString(updateProgress);
        FinhubLogger.info("【通知任务中心下载进度成功】taskId:{} 请求参数：{}", taskId, updateProgressJson);
        String updateProgressPostBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/outer/" + taskId + "/progress", updateProgressJson);
        FinhubLogger.info("【通知任务中心下载进度成功】taskId:{} 返回参数：{}", taskId, updateProgressPostBody);
    }

    /**
     * 获取当前时间的年月日
     *
     * @return
     */
    public String getTimeAtNow() {
        String data = DateUtils.format(new Date(), "yyyyMMdd");
        return data.substring(0, 4) + "/" + data.substring(4, 6) + "/" + data.substring(6, data.length()) + "/";
    }

    private void createAcctCompanySwitch(String companyId) {
        AcctCompanySwitchRespDTO acctCompanySwitchRespDTO = uCompanySwitchService.getAcctCompanySwitchByCompanyId(companyId);
        if (acctCompanySwitchRespDTO == null) {
            AcctCompanySwitchAddReqDTO acctCompanySwitchAddReqDTO = new AcctCompanySwitchAddReqDTO();
            acctCompanySwitchAddReqDTO.setCompanyId(companyId);
            uCompanySwitchService.createAcctCompanySwitch(acctCompanySwitchAddReqDTO);
        } else {
            if (acctCompanySwitchRespDTO.getAccountStatus() != CompanySwitchConstant.NORMAL.getKey()) {
                uCompanySwitchService.activeAcctCompanySwitch(companyId);
            }
        }
    }

    /**
     * 账户升级-新增对公账户的余额账户
     * 1. tb_account_public 对公账户表的记录都要入到余额账户表
     * 1.1 可能存在多条
     * 1.2 company_main_id-公司主体id不一样
     * 2.根据company_main_id 来筛选写入tb_account_general
     * 3.更新tb_account_public的account_general_id
     * <p>
     * <p>
     * 原始sql: insert into
     * tb_account_general(
     * account_general_id, company_id, company_main_id,company_main_type,company_name,
     * company_model,company_main_name,balance, create_time, update_time,
     * account_status,bank_name,bank_account_no,bank_acct_id,account_model
     * )
     * select
     * account_general_id,company_id,company_main_id,company_main_type,company_name,
     * company_model,bank_account_acct_name,0.00 as balance,create_time,update_time,
     * 0 as account_status,bank_account_name,bank_account_no,bank_account_no as bank_account_no1,2
     * from tb_account_public where company_id in ('5747fbc10f0e60e0709d8d7d');
     */
    private void upgradeAcctPublic(String companyId) {
        List<AccountGeneral> accountGeneralList = uAccountGeneralService.findByCompanyId(companyId);
        List<AccountPublic> accountPublicList = acctPublicDechService.queryAcctPublicByCompanyId(companyId);
        if (CollectionUtils.isEmpty(accountPublicList)) {
            return;
        }
        List<AccountPublic> generalFromPublicAcct = getNewAcctGeneralFromPublicAcct(accountPublicList, accountGeneralList);
        if (CollectionUtils.isEmpty(generalFromPublicAcct)) {
            return;
        }
        for (AccountPublic accountPublic : generalFromPublicAcct) {
            createGeneralAccountByAcctPublic(accountPublic);
        }
    }

    /**
     * 通过对公账户数据生成余额账户表数据
     *
     * @param accountPublic 对公账户po
     */
    private void createGeneralAccountByAcctPublic(AccountPublic accountPublic) {
        AcctGeneralUpgradeDTO acctGeneralUpgradeDTO = new AcctGeneralUpgradeDTO();
        acctGeneralUpgradeDTO.setAccountGeneralId(IDGen.genAccountGeneralId(accountPublic.getCompanyId()));
        acctGeneralUpgradeDTO.setCompanyId(accountPublic.getCompanyId());
        acctGeneralUpgradeDTO.setCompanyMainId(accountPublic.getCompanyMainId());
        acctGeneralUpgradeDTO.setCompanyMainType(accountPublic.getCompanyMainType());
        acctGeneralUpgradeDTO.setCompanyName(accountPublic.getCompanyName());
        acctGeneralUpgradeDTO.setCompanyModel(accountPublic.getCompanyModel());
        acctGeneralUpgradeDTO.setCompanyMainName(accountPublic.getBankAccountAcctName());
        acctGeneralUpgradeDTO.setBalance(BigDecimal.ZERO);
        acctGeneralUpgradeDTO.setCreateTime(new Date());
        acctGeneralUpgradeDTO.setUpdateTime(new Date());
        acctGeneralUpgradeDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
        acctGeneralUpgradeDTO.setBankName(accountPublic.getBankAccountName());
        acctGeneralUpgradeDTO.setBankAccountNo(accountPublic.getBankAccountNo());
        acctGeneralUpgradeDTO.setBankAcctId(accountPublic.getBankAccountNo());
        acctGeneralUpgradeDTO.setAccountModel(RECHARGE.getKey());
        acctGeneralUpgradeDTO.setDefaultTransferAccountType(FundAccountSubType.GENERAL_ACCOUNT.getKey());
        acctGeneralUpgradeDTO.setDirectAcctType(2);
        FinhubLogger.info("【新账户系统】【升级创建账户】参数：{}", JSON.toJSON(acctGeneralUpgradeDTO));
        String accountGeneralId = uAccountGeneralService.createUpgradeGeneralAccount(acctGeneralUpgradeDTO);
        acctPublicService.updateAccountGeneralIdByAccountPublicId(accountPublic.getAccountPublicId(), accountGeneralId);
        FinhubLogger.info("【新账户系统】【升级创建账户完成】参数：{},{}", accountGeneralId, JSON.toJSON(acctGeneralUpgradeDTO));
    }

    /**
     * 从对公账户中获取待新增的余额账户
     *
     * @param accountPublicList  对公账户list
     * @param accountGeneralList 余额账户list
     * @return List<String>
     */
    private List<AccountPublic> getNewAcctGeneralFromPublicAcct(List<AccountPublic> accountPublicList, List<AccountGeneral> accountGeneralList) {
        //如果余额账户列表为空，则当前对公账户list全部需要创建余额账户
        if (CollectionUtils.isEmpty(accountGeneralList)) {
            return accountPublicList;
        }
        Map<String, AccountPublic> toAddGeneralMap = new HashMap<>();
        for (AccountPublic accountPublic : accountPublicList) {
            for (AccountGeneral accountGeneral : accountGeneralList) {
                if (!(Objects.equals(accountPublic.getCompanyMainId(), accountGeneral.getCompanyMainId()) &&
                        Objects.equals(accountPublic.getCompanyId(), accountGeneral.getCompanyId()) &&
                        Objects.equals(accountPublic.getBankAccountNo(), accountGeneral.getBankAccountNo()) &&
                        Objects.equals(accountPublic.getBankAccountName(), accountGeneral.getBankName()))) {
                    if (!toAddGeneralMap.containsKey(accountPublic.getAccountPublicId())) {
                        toAddGeneralMap.put(accountPublic.getAccountPublicId(), accountPublic);
                    }
                }
            }
        }
        if (MapUtils.isEmpty(toAddGeneralMap)) {
            return null;
        }
        return new ArrayList<>(toAddGeneralMap.values());
    }

    /**
     * 账户升级-充值账户创建虚拟卡
     *
     * @param companyId 企业id
     */
    private void upgradeCompanyCard(String companyId) {
        //是否有可升级的商务充值账户基础数据
        AcctBusinessDebit acctBusinessDebit = getAcctBusinessDebitByFbt(companyId);
        if (acctBusinessDebit == null) {
            return;
        }
        AcctCompanyCard acctCompanyCard = getAcctCompanyCardByFbt(companyId);
        if (acctCompanyCard == null) {
            acctCompanyCard = createAcctCompanyCardByAcctBusinessDebit(acctBusinessDebit);
        }
        //升级4.0之后，虚拟卡不能再使用商务账户信息：
        // 账户ID不一样：一个为商务一个为虚拟卡，需要修改为虚拟卡ID
        // 账户ID一样:只能是两个都是虚拟卡ID,不会出现两个都是商务账户ID
        if (isNeedUpdateGateway(acctBusinessDebit.getAccountId(), acctCompanyCard.getAccountId())) {
            // 查询虚拟卡账户是否已经有网关记录
            AcctComGwAcctRespDTO acctComGwAcctRespDTO = getGatewayByBusinessDebit(companyId);
            if (acctComGwAcctRespDTO == null) {
                makeGatewayByAcctCompanyCard(acctCompanyCard);
                return;
            }
            upgradeOpenGatewayByAcctCompanyCard(acctCompanyCard, acctComGwAcctRespDTO);
        }
    }

    /**
     * 判断是否需要更新网关
     *
     * @param oldAccountId oldAccountId
     * @param newAccountId newAccountId
     * @return
     */
    private boolean isNeedUpdateGateway(String oldAccountId, String newAccountId) {
        //判断是否需要更新网关
        return !newAccountId.equals(oldAccountId);
    }

    private AcctBusinessDebit getAcctBusinessDebitByFbt(String companyId) {
        //是否有可升级的商务充值账户基础数据
        List<AcctBusinessDebit> acctBusinessDebitList = acctBusinessDebitService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(acctBusinessDebitList)) {
            return acctBusinessDebitList.stream().filter(
                    acctBusinessDebit -> (
                            BankNameEnum.FBT.getCode().equals(acctBusinessDebit.getBankName())
                    )

            ).findFirst().orElse(null);
        }
        return null;
    }

    private AcctCompanyCard getAcctCompanyCardByFbt(String companyId) {
        //是否已经创建过虚拟卡账户
        List<AcctCompanyCard> acctCompanyCardList = acctCompanyCardService.findByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(acctCompanyCardList)) {
            return acctCompanyCardList.stream().filter(
                    acctCompanyCard -> BankNameEnum.FBT.getCode().equals(acctCompanyCard.getBankName())
            ).findFirst().orElse(null);
        }
        return null;
    }

    /**
     * @param acctBusinessDebit 商务充值账户po
     * @return AcctCompanyCard
     */
    private AcctCompanyCard createAcctCompanyCardByAcctBusinessDebit(AcctBusinessDebit acctBusinessDebit) {
        AcctCompanyCardUpgradeDTO acctCompanyCardUpgradeDTO = new AcctCompanyCardUpgradeDTO();
        String suffix = acctBusinessDebit.getAccountGeneralId().substring(acctBusinessDebit.getAccountGeneralId().length() - 2);
        String dateString = DateUtil.getFormateDateString("yyyyMMddHHmmss", new Date());
        String accountId = "ABA" + dateString + RandomUtil.randomNumbers(5) + suffix;
        acctCompanyCardUpgradeDTO.setAccountId(accountId);
        acctCompanyCardUpgradeDTO.setAccountGeneralId(acctBusinessDebit.getAccountGeneralId());
        acctCompanyCardUpgradeDTO.setCompanyId(acctBusinessDebit.getCompanyId());
        acctCompanyCardUpgradeDTO.setCompanyMainId(acctBusinessDebit.getCompanyId());
        acctCompanyCardUpgradeDTO.setCompanyName(acctBusinessDebit.getCompanyName());
        acctCompanyCardUpgradeDTO.setCompanyModel(acctBusinessDebit.getCompanyModel());
        acctCompanyCardUpgradeDTO.setBalance(BigDecimal.ZERO);
        acctCompanyCardUpgradeDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
        Date date = new Date();
        acctCompanyCardUpgradeDTO.setCreateTime(date);
        acctCompanyCardUpgradeDTO.setUpdateTime(date);
        acctCompanyCardUpgradeDTO.setActiveStatus(ACTIVATE.getStatus());
        acctCompanyCardUpgradeDTO.setShowStatus(SHOW.getStatus());
        acctCompanyCardUpgradeDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
        acctCompanyCardUpgradeDTO.setCompanyName(acctBusinessDebit.getCompanyName());
        acctCompanyCardUpgradeDTO.setBankChannelName(BankNameEnum.CGB.getCode());
        acctCompanyCardUpgradeDTO.setBankAccountNo(acctBusinessDebit.getCompanyId());
        acctCompanyCardUpgradeDTO.setBankAcctId(acctBusinessDebit.getCompanyId());
        acctCompanyCardUpgradeDTO.setCompanyMainType(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey());
        acctCompanyCardUpgradeDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
        acctCompanyCardUpgradeDTO.setAccountModel(RECHARGE.getKey());
        acctCompanyCardUpgradeDTO.setBankName(BankNameEnum.FBT.getCode());
        FinhubLogger.info("【新账户系统】【升级创建虚拟卡账户】参数：{}", JSON.toJSONString(acctCompanyCardUpgradeDTO));
        AcctCompanyCard acctCompanyCard = uAcctCompanyCardService.createUpgradeAcctCompanyCard(acctCompanyCardUpgradeDTO);
        FinhubLogger.info("【新账户系统】【升级创建虚拟卡账户完成】参数：{},{}", acctBusinessDebit.getAccountGeneralId(), JSON.toJSONString(acctCompanyCard));
        return acctCompanyCard;
    }

    /**
     * 根据企业ID获取已经所有已经建好的网关数据
     *
     * @param companyId 企业ID
     * @return List<AcctComGwAcctRespDTO>
     */
    private AcctComGwAcctRespDTO getGatewayByBusinessDebit(String companyId) {
        AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctComGwByComIdReqDTO.setCompanyId(companyId);
        AcctComGwRespDTO acctComGwRespDTO = acctCompanyGatewayService.findActGwsByComId(acctComGwByComIdReqDTO);
        if (CollectionUtils.isNotEmpty(acctComGwRespDTO.getAcctComGwAcctRespDTOS())) {
            return acctComGwRespDTO.getAcctComGwAcctRespDTOS().stream().filter(
                    acctComGwAcctRespDTO ->
                            acctComGwAcctRespDTO.getAccountSubType() != null && FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey() == acctComGwAcctRespDTO.getAccountSubType()
                                    && acctComGwAcctRespDTO.getFundPlatform() != null && FundPlatformEnum.FBT.getKey() == acctComGwAcctRespDTO.getFundPlatform()
                                    && acctComGwAcctRespDTO.getBankName() != null && BankNameEnum.FBT.getCode().equals(acctComGwAcctRespDTO.getBankName())
            ).findFirst().orElse(null);
        }
        return null;
    }

    /**
     * 开启网关开关
     *
     * @param acctCompanyCard      待开虚拟卡
     * @param acctComGwAcctRespDTO 网关数据集合
     */
    private void upgradeOpenGatewayByAcctCompanyCard(AcctCompanyCard acctCompanyCard, AcctComGwAcctRespDTO acctComGwAcctRespDTO) {
        if (acctComGwAcctRespDTO != null) {
            AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
            acctComGwUpdateReqDTO.setAccountId(acctCompanyCard.getAccountId());
            acctComGwUpdateReqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            acctComGwUpdateReqDTO.setCompanyId(acctCompanyCard.getCompanyId());
            acctComGwUpdateReqDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
            acctComGwUpdateReqDTO.setCompanyMainId(acctComGwAcctRespDTO.getCompanyMainId());
            acctComGwUpdateReqDTO.setCompanyModel(acctComGwAcctRespDTO.getCompanyModel());
            acctComGwUpdateReqDTO.setBankName(acctComGwAcctRespDTO.getBankName());
            acctComGwUpdateReqDTO.setBankAccountNo(acctComGwAcctRespDTO.getBankAccountNo());
            acctComGwUpdateReqDTO.setRemark(acctComGwAcctRespDTO.getRemark());
            acctComGwUpdateReqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
            acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
        }
    }

    /**
     * 开启网关开关
     * @param acctCompanyCard 待开虚拟卡
     */
    private void makeGatewayByAcctCompanyCard(AcctCompanyCard acctCompanyCard){
            AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
            acctComGwUpdateReqDTO.setAccountId(acctCompanyCard.getAccountId());
            acctComGwUpdateReqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            acctComGwUpdateReqDTO.setCompanyId(acctCompanyCard.getCompanyId());
            acctComGwUpdateReqDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
            acctComGwUpdateReqDTO.setCompanyMainId(acctCompanyCard.getCompanyMainId());
            acctComGwUpdateReqDTO.setCompanyModel(acctCompanyCard.getCompanyModel());
            acctComGwUpdateReqDTO.setBankName(acctCompanyCard.getBankName());
            acctComGwUpdateReqDTO.setBankAccountNo(acctCompanyCard.getBankAccountNo());
            acctComGwUpdateReqDTO.setFundPlatform(FundPlatformEnum.FBT.getKey());
            acctCompanyGatewayService.updateOrAddActGw(acctComGwUpdateReqDTO);
    }

    /**
     * 获取文件名
     *
     * @param flow
     * @return
     */
    public String getExportFileName(List<AcctFlowRespDTO> flow) {
        String FileName = "";
        StringBuilder builder = new StringBuilder();
        try {
            //众邦银行托管账户
            String s = BankNameShowConfig.makeBankMainShowName(flow.get(0).getBankName(), null, flow.get(0).getAccountModel());
            builder.append(s);
            builder.append("_");
            //可支配余额账户
            builder.append(FundAccountSubType.getEnum(flow.get(0).getAccountSubType()).getAcctName());
            builder.append("_");
            String startDate = DateUtils.format(flow.get(0).getCreateTime(), "yyyyMMdd");
            //********
            builder.append(startDate);
            builder.append("_");
            //********
            String endDate = DateUtils.format(flow.get(flow.size() - 1).getCreateTime(), "yyyyMMdd");
            builder.append(endDate);
            builder.append("_");
            //173811
            String millTime = String.valueOf(System.currentTimeMillis());
            builder.append(millTime.substring(millTime.length() - 6));
            FileName = builder.toString();
        } catch (Exception e) {
            FinhubLogger.error("批量导出电子回单转换时间异常" + e);
        }
        return FileName + "-" + getRandomSixNum();
    }

    public String getLfExportFileName(AcctOptFlowReqDTO reqDTO) {
        String FileName = "";
        StringBuilder builder = new StringBuilder();
        try {
            String s = BankNameShowConfig.makeBankMainShowName(reqDTO.getBankName(), reqDTO.getBankAccountNo(), reqDTO.getAccountModel());
            builder.append(s);
            builder.append("_");
            //可支配余额账户
            builder.append(FundAccountSubType.getEnum(reqDTO.getAccountSubType()).getAcctName());
            builder.append("_");
            String startDate = DateUtils.format(reqDTO.getStartTime(), "yyyyMMdd");
            //********
            builder.append(startDate);
            builder.append("_");
            //********
            String endDate = DateUtils.format(reqDTO.getEndTime(), "yyyyMMdd");
            builder.append(endDate);
            builder.append("_");
            //173811
            String millTime = String.valueOf(System.currentTimeMillis());
            builder.append(millTime.substring(millTime.length() - 6));
            FileName = builder.toString();
        } catch (Exception e) {
            FinhubLogger.error("批量导出电子回单转换时间异常" + e);
        }
        return FileName + "-" + getRandomSixNum();
    }

    /**
     * 随机生成一位六位数为任务号
     *
     * @return
     */
    public String getRandomSixNum() {
        int x = (int) (Math.random() * 1000000);
        String s = x + "";
        List<String> list = new ArrayList<String>();
        if (s.length() < 6) {
            for (int i = 0; i < 6 - s.length(); i++) {
                list.add("0");
            }
        }
        String result = list.toString().replace("[", "").replace("]", "").
                replace(",", "").replace(" ", "") + s;
        return result;
    }

    /**
     *
     * @param iMessage 消息
     * @param fundFreezenFlow 冻结池流水
     * @param accountModel 账户模式
     */
    private void asyncBank(KafkaDechTradeResultMsg iMessage, FundFreezenFlow fundFreezenFlow,Integer accountModel) {
        String bizNo = fundFreezenFlow.getBizNo();
        String reBizNo = fundFreezenFlow.getReBizNo();
        FundFreezenFlow updateFundFreezenFlow = new FundFreezenFlow();
        updateFundFreezenFlow.setId(fundFreezenFlow.getId());
        if (AccountStatusEnum.isSuccess(iMessage.getTxnSt())) {
            updateFundFreezenFlow.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
            updateFundFreezenFlow.setSyncBankAmount(iMessage.getOperationAmount());
            updateFundFreezenFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
            updateFundFreezenFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
            updateFundFreezenFlow.setBankTransNo(iMessage.getSysOrdNo());
            int updateSyncFlow = fundFreezenFlowService.updateByIdSelective(updateFundFreezenFlow);
            if (updateSyncFlow != 1) {
                //日志  钉钉
                FinhubLogger.error("【担保账户，上账状态更新失败】data：{}", JsonUtils.toJson(updateFundFreezenFlow));
                dingDingMsgService.sendMsg("【担保账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
            }
            //判断是消费还是退款
            Integer operationType = fundFreezenFlow.getOperationType();
            if(FreezenChangeType.isPay(operationType)){
                updateGuaranteeBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, bizNo,FundPlatAcctOptType.VOUCHER_CONSUME, null);
                updateReceiptBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, bizNo,FundPlatAcctOptType.VOUCHER_CONSUME, null);
                // 分贝券消费，不发对账消息 QX 2022-01-13
                return;
            }
            if(FreezenChangeType.isRefund(operationType)){
                updateGuaranteeBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, bizNo,FundPlatAcctOptType.VOUCHER_REFUND, null);
                updateReceiptBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, bizNo,FundPlatAcctOptType.VOUCHER_REFUND, null);
                // 分贝券退款，不发对账消息 QX 2022-01-13
                return;
            }
            if(FreezenChangeType.isUnfreezing(operationType)){
                BankTradeReceiptQryReqDto bankTradeReceiptQryReqDto = new BankTradeReceiptQryReqDto();
                bankTradeReceiptQryReqDto.setBankTransNo(iMessage.getSysOrdNo());
                bankTradeReceiptQryReqDto.setBankName(fundFreezenFlow.getBankName());
                bankTradeReceiptQryReqDto.setCompanyAccountId(fundFreezenFlow.getBankAcctId());
                bankTradeReceiptQryReqDto.setTradeTime(new Date());
                bankTradeReceiptQryReqDto.setTxnType(BankTradeType.getBankTypeByCode(BankTradeType.RECHARGE.getCode(), iMessage.getBankName()));
                String costImageUrl = null;
                try {
                    // 中信暂时下掉实时获取电子回单
                    if (!BankNameEnum.isCitic(fundFreezenFlow.getBankName())) {
                        BankTradeReceiptQryRespDto bankTradeReceiptQryRespDto = iBankSearchService.queryTxnReceipt(bankTradeReceiptQryReqDto);
                        if (Objects.nonNull(bankTradeReceiptQryRespDto) && !com.fenbeitong.finhub.common.utils.StringUtils.isBlank(bankTradeReceiptQryRespDto.getDownloadUrl())) {
                            costImageUrl = bankTradeReceiptQryRespDto.getDownloadUrl();
                        }
                    }
                } catch (Exception ex) {
                    FinhubLogger.error("获取电子回单失败：参数：{}", bankTradeReceiptQryReqDto);
                }

                //解冻到个人账户操作
                updateGuaranteeBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, bizNo,FundPlatAcctOptType.VOUCHER_RECALL,costImageUrl);
                //增加个人账户
                if(FundAccountModelType.isCredit(iMessage.getAccountModel())){
                    AcctIndividualCreditFlow individualCreditFlow = findAcctIndividualCreditFlow(bizNo, FundAcctDebitOptType.FROZEN_VOUCHER_RECALL.getKey());
                    if(Objects.isNull(individualCreditFlow)){
                        FinhubLogger.error("【个人账户，上账状态更新失败】分贝券回收，bizNo：{}",bizNo);
                        throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
                    }
                    updateIndiviAcctCreditFlow(iMessage, individualCreditFlow, FundAcctSyncBankStatus.SYNC);
                    //中信更新电子回单
//                    updateIndiviAcctCreditCostImgFlow(iMessage, individualCreditFlow,fundFreezenFlow);
                    /**
                     * 返回前，发送对账消息。更新上帐流水号
                     * QX 2021-12-22
                     */
                    successSend(iMessage, fundFreezenFlow);
                    return;
                }
                if(FundAccountModelType.isRecharge(iMessage.getAccountModel())){
                    AcctIndividualDebitFlow individualDebitFlow = getAcctIndividualDebitFlow(bizNo,reBizNo, FundAcctDebitOptType.FROZEN_VOUCHER_RECALL.getKey());
                    if(Objects.isNull(individualDebitFlow)){
                        FinhubLogger.error("【个人账户，上账状态更新失败】分贝券回收，bizNo：{}",bizNo);
                        throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
                    }
                    updateIndiviAcctDebitFlow(iMessage, individualDebitFlow, FundAcctSyncBankStatus.SYNC, costImageUrl);
                    //中信更新电子回单
//                    updateIndiviAcctDebitCostImgFlow(iMessage, individualDebitFlow,fundFreezenFlow);
                    /**
                     * 返回前，发送对账消息。更新上帐流水号
                     * QX 2021-12-22
                     */
                    successSend(iMessage, fundFreezenFlow);
                }
                return;
            }
            if(FreezenChangeType.isUnfreezing2Fbt(operationType)){
                //解冻到FBT操作
                updateGuaranteeBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, bizNo,FundPlatAcctOptType.VOUCHER_RECALL, null);
                //增加平台收款账户
                updateReceiptBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, bizNo,FundPlatAcctOptType.VOUCHER_RECALL_TO_RECEIPT, null);
            }

        } else if (AccountStatusEnum.isFail(iMessage.getTxnSt())) {
            int callBankNum = Objects.isNull(fundFreezenFlow.getCallbackNum()) ? 0 : fundFreezenFlow.getCallbackNum();
            callBankNum += 1;
            double minutes = Math.pow(2, callBankNum);
            updateFundFreezenFlow.setCallbackNum(callBankNum);
            updateFundFreezenFlow.setCallbackNext(org.apache.commons.lang3.time.DateUtils.addMinutes(new Date(), (int) minutes));
            //发送钉钉提醒
            if (callBankNum > 10) {
                FinhubLogger.error("【fenbei-pay】分贝券消费资金流水上账超过10次，不再自动上账，需手动处理,资金流水id：{}", fundFreezenFlow.getFreezenFlowId());
                String msgError = "【fenbei-pay】分贝券消费资金流水上账超过10次，不再自动上账，需手动处理,资金流水id :" + fundFreezenFlow.getFreezenFlowId();
                dingDingMsgService.sendMsg(msgError);
            } else {
                int updateSyncFlow = fundFreezenFlowService.updateByIdSelective(updateFundFreezenFlow);
                if (updateSyncFlow != 1) {
                    //日志  钉钉
                    FinhubLogger.error("【担保账户，上账状态更新失败】data：{}", JsonUtils.toJson(updateFundFreezenFlow));
                    dingDingMsgService.sendMsg( "【担保账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
                }
            }
            /**
             * 消费消息，不发送对账消息（方案变更，发消息）
             * QX 2021-12-13 FBT-9264
             */
            //上账发送消息--自动对账
            KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
            try {
                BeanUtils.copyProperties(fundFreezenFlow, kafkaAutoAcctCheckingMsg);
                kafkaAutoAcctCheckingMsg.setAccountFlowId(fundFreezenFlow.getFreezenFlowId());
                kafkaAutoAcctCheckingMsg.setSyncBankStatus(FundAcctSyncBankStatus.SYNC_FAILED.getCode());
                kafkaAutoAcctCheckingMsg.setSyncBankAmount(iMessage.getOperationAmount());
                if(iMessage.getFinishTime() != null){
                    kafkaAutoAcctCheckingMsg.setSyncBankTime(DateUtil.getDateFromString(iMessage.getFinishTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                kafkaAutoAcctCheckingMsg.setSyncBankTransNo(iMessage.getSyncBankTransNo());
                kafkaAutoAcctCheckingMsg.setBankTransNo(iMessage.getSysOrdNo());
                autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
            }catch (Exception ex){
                FinhubLogger.error("个人授信账户上账，自动对账消息发送失败:{}", JSON.toJSONString(kafkaAutoAcctCheckingMsg));
                FinhubLogger.error("asyncBank,对账消息发送失败", ex);
            }
        }
    }

    /**
     * 统一发送对账消息。
     * @param iMessage
     * @param fundFreezenFlow
     */
    private void successSend(KafkaDechTradeResultMsg iMessage, FundFreezenFlow fundFreezenFlow){
        /**
         * 消费消息，不发送对账消息（方案变更，发消息）
         * QX 2021-12-13 FBT-9264
         */
        //上账发送消息--自动对账
        KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
        try {
            BeanUtils.copyProperties(fundFreezenFlow, kafkaAutoAcctCheckingMsg);
            kafkaAutoAcctCheckingMsg.setAccountFlowId(fundFreezenFlow.getFreezenFlowId());
            kafkaAutoAcctCheckingMsg.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
            kafkaAutoAcctCheckingMsg.setSyncBankAmount(iMessage.getOperationAmount());
            if(iMessage.getFinishTime() != null){
                kafkaAutoAcctCheckingMsg.setSyncBankTime(DateUtil.getDateFromString(iMessage.getFinishTime(),"yyyy-MM-dd HH:mm:ss"));
            }
            kafkaAutoAcctCheckingMsg.setSyncBankTransNo(iMessage.getSyncBankTransNo());
            kafkaAutoAcctCheckingMsg.setBankTransNo(iMessage.getSysOrdNo());
            autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
        }catch (Exception ex){
            FinhubLogger.error("个人授信账户上账，自动对账消息发送失败:{}", JSON.toJSONString(kafkaAutoAcctCheckingMsg));
            FinhubLogger.error("asyncBank,对账消息发送失败", ex);
        }
    }

    private void syncBankDebit(KafkaDechTradeResultMsg iMessage, FundFreezenFlow fundFreezenFlow) {
        //银行上账成功，不区分发券还是回收券,只更新流水上账状态
        String bizNo = fundFreezenFlow.getBizNo();
        if (AccountStatusEnum.isSuccess(iMessage.getTxnSt())) {
            AcctIndividualDebitFlow individualDebitFlow = getAcctIndividualDebitFlow(bizNo,null, FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey());
            AcctIndividualDebitFlow updateFlow = new AcctIndividualDebitFlow();
            updateFlow.setId(individualDebitFlow.getId());
            updateFlow.setBankTransNo(iMessage.getSysOrdNo());
            updateFlow.setSyncBankAmount(Objects.isNull(iMessage.getOperationAmount()) ? BigDecimal.ZERO : iMessage.getOperationAmount());
            updateFlow.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
            updateFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
            updateFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
            BankTradeReceiptQryReqDto bankTradeReceiptQryReqDto = new BankTradeReceiptQryReqDto();
            bankTradeReceiptQryReqDto.setBankTransNo(iMessage.getSysOrdNo());
            bankTradeReceiptQryReqDto.setBankName(individualDebitFlow.getBankName());
            bankTradeReceiptQryReqDto.setCompanyAccountId(individualDebitFlow.getBankAcctId());
            bankTradeReceiptQryReqDto.setTradeTime(new Date());
            bankTradeReceiptQryReqDto.setTxnType(BankTradeType.getBankTypeByCode(BankTradeType.RECHARGE.getCode(), individualDebitFlow.getBankName()));
            try {
                // 中信暂时下掉实时获取电子回单
                if(!BankNameEnum.isCitic(individualDebitFlow.getBankName())) {
                    BankTradeReceiptQryRespDto bankTradeReceiptQryRespDto = iBankSearchService.queryTxnReceipt(bankTradeReceiptQryReqDto);
                    if (Objects.nonNull(bankTradeReceiptQryRespDto) && !com.fenbeitong.finhub.common.utils.StringUtils.isBlank(bankTradeReceiptQryRespDto.getDownloadUrl())) {
                        updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_SUCE.getKey());
                        updateFlow.setCostImageUrl(bankTradeReceiptQryRespDto.getDownloadUrl());
                    } else {
                        if (BankNameEnum.isSpa(individualDebitFlow.getBankName())) {
                            updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_NON.getKey());
                        } else {
                            updateFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_FAILE.getKey());
                        }
                    }
                }
            } catch (Exception ex) {
                FinhubLogger.error("获取电子回单失败：参数：{}", bankTradeReceiptQryReqDto);
            }
            updateIndiviAcctDebitFlow(iMessage, individualDebitFlow, FundAcctSyncBankStatus.SYNC, updateFlow.getCostImageUrl());
            //跟新电子回单，失败吃掉异常，不回滚，非重要数据，可重新生成
//            updateIndiviAcctDebitCostImgFlow(iMessage,individualDebitFlow,fundFreezenFlow);
            //冻结池成功
            updateFreezenFlow(iMessage, FundAcctSyncBankStatus.SYNC);
            //担保账户成功
            updateGuaranteeBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC, individualDebitFlow.getBizNo(),FundPlatAcctOptType.VOUCHER_GRANT, updateFlow.getCostImageUrl());
            /**
             * 方案调整：再次发送对账消息
             * QX 2021-12-21 SERVER-4902
             */
            //上账发送消息--自动对账
            KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
            try {
                BeanUtils.copyProperties(fundFreezenFlow, kafkaAutoAcctCheckingMsg);
                kafkaAutoAcctCheckingMsg.setAccountFlowId(fundFreezenFlow.getFreezenFlowId());
                kafkaAutoAcctCheckingMsg.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
                kafkaAutoAcctCheckingMsg.setSyncBankAmount(iMessage.getOperationAmount());
                if(iMessage.getFinishTime() != null){
                    kafkaAutoAcctCheckingMsg.setSyncBankTime(DateUtil.getDateFromString(iMessage.getFinishTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                kafkaAutoAcctCheckingMsg.setSyncBankTransNo(iMessage.getSyncBankTransNo());
                kafkaAutoAcctCheckingMsg.setBankTransNo(iMessage.getSysOrdNo());
                autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
            }catch (Exception ex){
                FinhubLogger.error("syncBankDebit，自动对账消息发送失败:{}", JSON.toJSONString(kafkaAutoAcctCheckingMsg));
                FinhubLogger.error("syncBankDebit，对账消息发送失败", ex);
            }
            return;
        }
        //银行上账失败，区分发券还是回收券，发券与回收券的方向逻辑不同，更新原流水状态为失败，并生成反向流水，并且操作账户余额。
        if (AccountStatusEnum.isFail(iMessage.getTxnSt())) {
            AcctIndividualDebitFlow individualDebitFlow = getAcctIndividualDebitFlow(bizNo,null, FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey());
            // 发券失败
            if (FreezenChangeType.isFreezing(fundFreezenFlow.getOperationType())) {
                //个人账户流水同步失败
                updateIndiviAcctDebitFlow(iMessage, individualDebitFlow, FundAcctSyncBankStatus.SYNC_FAILED, null);
                //冻结池流水同步失败
                updateFreezenFlow(iMessage, FundAcctSyncBankStatus.SYNC_FAILED);
                //担保账户流水同步失败
                BankAcctFlow bankAcctFlowOrigin = updateGuaranteeBankAcctFlow(iMessage, FundAcctSyncBankStatus.SYNC_FAILED, individualDebitFlow.getBizNo(), FundPlatAcctOptType.VOUCHER_GRANT, null);
                //反向操作
                BigDecimal operationAmount = individualDebitFlow.getOperationAmount().abs();
                //    个人账户增加
                revserseIndiDebitAcctAdd(individualDebitFlow, operationAmount);
                //    担保账户减少
                reverseIndiDebitBankAcctReduce(individualDebitFlow, bankAcctFlowOrigin, operationAmount);
                //    冻结池减少
                reverseIndiDebitFreezenReduce(iMessage, individualDebitFlow, operationAmount);
            }
            return;
        }
    }


    private void reverseIndiDebitFreezenReduce(KafkaDechTradeResultMsg iMessage, AcctIndividualDebitFlow individualDebitFlow, BigDecimal operationAmount) {
        FundFreezenFlow freezenFlow = fundFreezenFlowService.queryByFreezenFlowId(iMessage.getAccountFlowId());
        if(Objects.isNull(freezenFlow)){
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        FundFreezen fundFreezen = fundFreezenService.queryByFreezenId(freezenFlow.getFreezeBudgetId());
        if(Objects.isNull(fundFreezen)){
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        boolean unfreezeSucc = fundFreezenService.unfreezeBudget(fundFreezen, operationAmount);
        if(!unfreezeSucc){
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        FundFreezen after = fundFreezenService.queryByFreezenId(freezenFlow.getFreezeBudgetId());
        FundFreezenFlow reverseFreezenFlow = new FundFreezenFlow();
        BeanUtils.copyProperties(freezenFlow,reverseFreezenFlow);
        reverseFreezenFlow.setId(null);
        String flowId = IDGen.genAccountSubFlowId(individualDebitFlow.getCompanyId());
        reverseFreezenFlow.setFreezenFlowId(flowId);
        reverseFreezenFlow.setOperationType(FreezenChangeType.UNFREEZING.getKey());
        reverseFreezenFlow.setOperationAmount(operationAmount.multiply(new BigDecimal(-1)));
        reverseFreezenFlow.setOperationTypeDesc(FreezenChangeType.UNFREEZING.getValue());
        reverseFreezenFlow.setFreezeBalance(after.getFreezeBalance());
        // 广发特殊处理
        if (BankNameEnum.isCgb(freezenFlow.getBankName())) {
            reverseFreezenFlow.setSyncBankTransNo(null);
        }
        int insertFreezeFlow = fundFreezenFlowService.saveFreezenFlow(reverseFreezenFlow);
        if(insertFreezeFlow != 1){
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
    }

    private void reverseIndiDebitBankAcctReduce(AcctIndividualDebitFlow individualDebitFlow, BankAcctFlow bankAcctFlowOrigin, BigDecimal operationAmount) {
        String bankName = individualDebitFlow.getBankName();
        if(BankNameEnum.guaranteeByPlatform(bankName)){
//            BankAcct palteGuarantAcct = bankAcctService.findByBankNameAndType(bankName, BankAcctTypeEnum.GUARANTEE_BANK_ACCOUNT.getCode());
            BankAcct palteGuarantAcct = uBankAcctService.findBankAcct(bankName, BankAcctTypeEnum.GUARANTEE_BANK_ACCOUNT.getCode(), individualDebitFlow.getCompanyId(), individualDebitFlow.getOrderType(), 2);

            if(Objects.isNull(palteGuarantAcct)){
                throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
            }
            int reduceBalanceByAccountId = bankAcctService.reduceBalanceByAccountId(palteGuarantAcct.getAccountId(), operationAmount);
            if(reduceBalanceByAccountId != 1){
                throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
            }
//            BankAcct after = bankAcctService.findByBankNameAndType(bankName, BankAcctTypeEnum.GUARANTEE_BANK_ACCOUNT.getCode());
            BankAcct after = uBankAcctService.findBankAcct(bankName, BankAcctTypeEnum.GUARANTEE_BANK_ACCOUNT.getCode(), individualDebitFlow.getCompanyId(), individualDebitFlow.getOrderType(), 2);

            BankAcctFlow bankAcctFlow = new BankAcctFlow();
            BeanUtils.copyProperties(bankAcctFlowOrigin,bankAcctFlow);
            String flowId = IDGen.genAccountSubFlowId(individualDebitFlow.getCompanyId());
            bankAcctFlow.setBankAcctFlowId(flowId);
            bankAcctFlow.setTradeType(FundPlatAcctOptType.VOUCHER_GRANT_FAILED.getTradeType().getCode());
            bankAcctFlow.setOperationType(FundPlatAcctOptType.VOUCHER_GRANT_FAILED.getKey());
            bankAcctFlow.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_GRANT_FAILED.getDesc());
            bankAcctFlow.setOperationAmount(FundPlatAcctOptType.VOUCHER_GRANT_FAILED.getPlusMinusNum(operationAmount));
            bankAcctFlow.setBalance(after.getBalance());
            bankAcctFlow.setCurrentBalance(palteGuarantAcct.getBalance());
            int reverseBankAcctFlow = bankAcctFlowService.saveFlow(bankAcctFlow);
            if(reverseBankAcctFlow != 1){
                throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
            }
        }
    }

    private void revserseIndiDebitAcctAdd(AcctIndividualDebitFlow individualDebitFlow,BigDecimal operationAmount) {
        AcctIndividualDebit before = acctIndividualDebitService.findAccountByAccountId(individualDebitFlow.getAccountId());
        int addBalanceByAccountId = acctIndividualDebitService.addBalanceByAccountId(individualDebitFlow.getAccountId(), operationAmount);
        if (addBalanceByAccountId != 1) {
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        AcctIndividualDebit after = acctIndividualDebitService.findAccountByAccountId(individualDebitFlow.getAccountId());
        AcctIndividualDebitFlow reverseFlow = new AcctIndividualDebitFlow();
        BeanUtils.copyProperties(individualDebitFlow, reverseFlow);
        String flowId = IDGen.genAccountSubFlowId(individualDebitFlow.getCompanyId());
        reverseFlow.setAccountFlowId(flowId);
        reverseFlow.setTradeType(FundAcctCreditOptType.FROZEN_VOUCHER_GRANT_FAILE.getTradeType().getCode());
        reverseFlow.setOperationType(FundAcctCreditOptType.FROZEN_VOUCHER_GRANT_FAILE.getKey());
        reverseFlow.setOperationAmount(FundAcctCreditOptType.FROZEN_VOUCHER_GRANT_FAILE.getPlusMinusNum(operationAmount));
        reverseFlow.setOperationDescription(FundAcctCreditOptType.FROZEN_VOUCHER_GRANT_FAILE.getDesc());
        reverseFlow.setBalance(after.getBalance());
        reverseFlow.setCurrentBalance(before.getBalance());
        //分贝券发放失败反向流水，电子回单为无需生成状态
        reverseFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_ON.getKey());
        int insertIndiDebitFlow = acctIndividualDebitFlowService.saveAccountIndividualDebitFlow(reverseFlow);
        if (insertIndiDebitFlow != 1) {
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
    }


    private void updateIndiviAcctDebitFlow(KafkaDechTradeResultMsg iMessage, AcctIndividualDebitFlow individualDebitFlow, FundAcctSyncBankStatus fundAcctSyncBankStatus, String costImageUrl) {
        AcctIndividualDebitFlow updateIndividualDebitFlow = new AcctIndividualDebitFlow();
        updateIndividualDebitFlow.setId(individualDebitFlow.getId());
        updateIndividualDebitFlow.setSyncBankStatus(fundAcctSyncBankStatus.getCode());
        updateIndividualDebitFlow.setSyncBankAmount(iMessage.getOperationAmount());
        updateIndividualDebitFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        updateIndividualDebitFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
        updateIndividualDebitFlow.setBankTransNo(iMessage.getSysOrdNo());
        // 电子回单
        updateIndividualDebitFlow.setCostImageStatus(StringUtils.isBlank(costImageUrl) ? FundAcctCostImageStatus.COST_IMAGE_NON.getKey() : FundAcctCostImageStatus.COST_IMAGE_SUCE.getKey());
        updateIndividualDebitFlow.setCostImageUrl(costImageUrl);
        updateIndividualDebitFlow.setCostImageTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        //分贝券发放失败反向流水，电子回单为无需生成状态
        if(FundAcctSyncBankStatus.syncFailedNew(fundAcctSyncBankStatus.getCode())) {
            updateIndividualDebitFlow.setCostImageStatus(FundAcctCostImageStatus.COST_IMAGE_ON.getKey());
        }
        int updateSyncFlow = uAcctIndividualDebitFlowService.updateByIdSelective(updateIndividualDebitFlow);
        if (updateSyncFlow != 1) {
            //日志  钉钉
            FinhubLogger.error("【个人充值账户，上账状态更新失败】data：{}", JsonUtils.toJson(updateIndividualDebitFlow));
            dingDingMsgService.sendMsg( "【个人充值账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
        }
    }


    private void updateIndiviAcctCreditFlow(KafkaDechTradeResultMsg iMessage, AcctIndividualCreditFlow individualCreditFlow, FundAcctSyncBankStatus fundAcctSyncBankStatus) {
        AcctIndividualCreditFlow updateIndividualCreditFlow = new AcctIndividualCreditFlow();
        updateIndividualCreditFlow.setId(individualCreditFlow.getId());
        updateIndividualCreditFlow.setSyncBankStatus(fundAcctSyncBankStatus.getCode());
        updateIndividualCreditFlow.setSyncBankAmount(iMessage.getOperationAmount());
        updateIndividualCreditFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        updateIndividualCreditFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
        updateIndividualCreditFlow.setBankTransNo(iMessage.getSysOrdNo());
        int updateSyncFlow = uAcctIndividualCreditFlowService.updateByIdSelective(updateIndividualCreditFlow);
        if (updateSyncFlow != 1) {
            //日志  钉钉
            FinhubLogger.error("【个人授信账户，上账状态更新失败】data：{}", JsonUtils.toJson(updateIndividualCreditFlow));
            dingDingMsgService.sendMsg("【个人授信账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
    }


    private AcctIndividualDebitFlow getAcctIndividualDebitFlow(String bizNo,String reBizNo, Integer operationType) {

        List<AcctIndividualDebitFlow> acctIndividualDebitFlows;
        if (reBizNo == null){
            acctIndividualDebitFlows = uAcctIndividualDebitFlowService.queryByBizNoAndOpTypeAndSyncStatus(bizNo, operationType, FundAcctSyncBankStatus.UN_SYNC.getCode());
        }else {
            acctIndividualDebitFlows = uAcctIndividualDebitFlowService.queryByBizNoAndReBizNoAndOpTypeAndSyncStatus(bizNo,reBizNo, operationType, FundAcctSyncBankStatus.UN_SYNC.getCode());
        }

        if (CollectionUtils.isEmpty(acctIndividualDebitFlows)) {
            FinhubLogger.error("个人充值账户分贝券发放回收上账失败,无对应的流水,bizNo :{}" ,bizNo);
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        if (acctIndividualDebitFlows.size() > 1) {
            //同一次发放有多条流水
            FinhubLogger.error("个人充值账户分贝券发放回收上账失败,对应流水存在多条,bizNo :{}" ,bizNo);
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        return acctIndividualDebitFlows.get(0);
    }

    private AcctIndividualCreditFlow findAcctIndividualCreditFlow(String bizNo, Integer operationType) {
        //更新个人授信账户流水上账状态
        List<AcctIndividualCreditFlow> acctIndividualCreditFlows = uAcctIndividualCreditFlowService.queryByBizNoAndOpTypeAndSyncStatus(bizNo, operationType, FundAcctSyncBankStatus.UN_SYNC.getCode());

        if (CollectionUtils.isEmpty(acctIndividualCreditFlows)) {
            //无流水
            FinhubLogger.error("个人授信账户分贝券发放回收上账失败,无对应的流水,bizNo :{}" , bizNo);
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        if (acctIndividualCreditFlows.size() > 1) {
            //同一次发放有多条流水
            FinhubLogger.error("个人授信账户分贝券发放回收上账失败,对应流水存在多条,bizNo :{}" ,bizNo);
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        return acctIndividualCreditFlows.get(0);
    }

    private void updateFreezenFlow(KafkaDechTradeResultMsg iMessage, FundAcctSyncBankStatus fundAcctSyncBankStatus) {
        FundFreezenFlow freezenFlow = fundFreezenFlowService.queryByFreezenFlowId(iMessage.getAccountFlowId());
        if (Objects.isNull(freezenFlow) ||FundAcctSyncBankStatus.hasSyncNew(freezenFlow.getSyncBankStatus())) {
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        FundFreezenFlow updateFreezenFlow = new FundFreezenFlow();
        updateFreezenFlow.setId(freezenFlow.getId());
        updateFreezenFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        updateFreezenFlow.setSyncBankAmount(iMessage.getOperationAmount());
        updateFreezenFlow.setSyncBankStatus(fundAcctSyncBankStatus.getCode());
        updateFreezenFlow.setSyncBankTransNo(iMessage.getSysOrdNo());
        int updateByIdSelective = fundFreezenFlowService.updateByIdSelective(updateFreezenFlow);
        if (updateByIdSelective != 1) {
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
    }

    private BankAcctFlow updateReceiptBankAcctFlow(KafkaDechTradeResultMsg iMessage, FundAcctSyncBankStatus fundAcctSyncBankStatus, String bizNo,FundPlatAcctOptType fundPlatAcctOptType, String costImageUrl) {
        BankAcctFlow  receiptBankAcctFlow = null;
        if(Objects.equals(fundPlatAcctOptType,FundPlatAcctOptType.VOUCHER_REFUND) || Objects.equals(fundPlatAcctOptType,FundPlatAcctOptType.PUBLIC_CONSUME_REFUND)){
            receiptBankAcctFlow = bankAcctFlowService.queryByBizNoAndOpeTypeAndSynStatusRefund(bizNo, fundPlatAcctOptType.getKey(), BankAcctTypeEnum.RECEIPT_BANK_ACCOUNT.getCode(), iMessage.getTxnId());
        }else {
            receiptBankAcctFlow = bankAcctFlowService.queryByBizNoAndOpeTypeAndSynStatus(bizNo, fundPlatAcctOptType.getKey(), BankAcctTypeEnum.RECEIPT_BANK_ACCOUNT.getCode(), iMessage.getTxnId());
        }
        if (Objects.isNull(receiptBankAcctFlow)) {
            FinhubLogger.warn("【平台担保/收款账户，不存在为同步流水】data：{}", JsonUtils.toJson(iMessage));
            return null;
        }
        //更新收款账户流水
        BankAcctFlow updateBankAcctFlow = new BankAcctFlow();
        updateBankAcctFlow.setSyncBankStatus(fundAcctSyncBankStatus.getCode());
        updateBankAcctFlow.setSyncBankAmount(Objects.isNull(iMessage.getOperationAmount()) ? BigDecimal.ZERO : iMessage.getOperationAmount());
        updateBankAcctFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        updateBankAcctFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
        updateBankAcctFlow.setBankTransNo(iMessage.getSysOrdNo());
        // 电子回单
        updateBankAcctFlow.setCostImageStatus(StringUtils.isBlank(costImageUrl) ? FundAcctCostImageStatus.COST_IMAGE_NON.getKey() : FundAcctCostImageStatus.COST_IMAGE_SUCE.getKey());
        updateBankAcctFlow.setCostImageUrl(costImageUrl);
        updateBankAcctFlow.setCostImageTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        updateBankAcctFlow.setId(receiptBankAcctFlow.getId());
        int receiptFlow = bankAcctFlowService.updateByIdSelective(updateBankAcctFlow);
        if (receiptFlow != 1) {
            FinhubLogger.error("【平台担保/收款账户，上账状态更新失败】data：{}", JsonUtils.toJson(receiptBankAcctFlow));
            dingDingMsgService.sendMsg("【平台担保/收款账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        return receiptBankAcctFlow;
    }

    private BankAcctFlow updateGuaranteeBankAcctFlow(KafkaDechTradeResultMsg iMessage, FundAcctSyncBankStatus fundAcctSyncBankStatus, String bizNo,FundPlatAcctOptType fundPlatAcctOptType, String costImageUrl) {
        BankAcctFlow guaranteeBankAcctFlow = null;
        if(Objects.equals(fundPlatAcctOptType,FundPlatAcctOptType.VOUCHER_REFUND) || Objects.equals(fundPlatAcctOptType,FundPlatAcctOptType.VOUCHER_RECALL)){
            guaranteeBankAcctFlow =bankAcctFlowService.queryByBizNoAndOpeTypeAndSynStatusRefund(bizNo,fundPlatAcctOptType.getKey(),BankAcctTypeEnum.GUARANTEE_BANK_ACCOUNT.getCode(),iMessage.getTxnId());
        }else {
            guaranteeBankAcctFlow = bankAcctFlowService.queryByBizNoAndOpeTypeAndSynStatus(bizNo, fundPlatAcctOptType.getKey(), BankAcctTypeEnum.GUARANTEE_BANK_ACCOUNT.getCode(), iMessage.getTxnId());
        }
        if (Objects.isNull(guaranteeBankAcctFlow)) {
            FinhubLogger.warn("【平台担保/收款账户，不存在为同步流水】data：{}", JsonUtils.toJson(iMessage));
            return null;
        }
        BankAcctFlow updateBankAcctFlow = new BankAcctFlow();
        updateBankAcctFlow.setSyncBankStatus(fundAcctSyncBankStatus.getCode());
        updateBankAcctFlow.setSyncBankAmount(Objects.isNull(iMessage.getOperationAmount()) ? BigDecimal.ZERO : iMessage.getOperationAmount());
        updateBankAcctFlow.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        updateBankAcctFlow.setSyncBankTransNo(iMessage.getSyncBankTransNo());
        updateBankAcctFlow.setBankTransNo(iMessage.getSysOrdNo());
        // 电子回单
        updateBankAcctFlow.setCostImageStatus(StringUtils.isBlank(costImageUrl) ? FundAcctCostImageStatus.COST_IMAGE_NON.getKey() : FundAcctCostImageStatus.COST_IMAGE_SUCE.getKey());
        updateBankAcctFlow.setCostImageUrl(costImageUrl);
        updateBankAcctFlow.setCostImageTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        //更新担保账户流水
        updateBankAcctFlow.setId(guaranteeBankAcctFlow.getId());
        int guaranteeFlow = bankAcctFlowService.updateByIdSelective(updateBankAcctFlow);
        if (guaranteeFlow != 1) {
            FinhubLogger.error("【平台担保/收款账户，上账状态更新失败】data：{}", JsonUtils.toJson(guaranteeBankAcctFlow));
            dingDingMsgService.sendMsg( "【平台担保/收款账户，上账状态更新失败】流水Id：" + iMessage.getAccountFlowId());
            throw new FinPayException(GlobalResponseCode.BANK_SYNC_ERROR);
        }
        // 发送对账消息
        sendAutoAcctCheckingMsg(iMessage,guaranteeBankAcctFlow);
        return guaranteeBankAcctFlow;
    }

    @Override
    public void syncBankConsume(KafkaBankConsumeTaskMsg iMessage) {
        if (Objects.isNull(iMessage)) {
            FinhubLogger.info("【kafka消费：因公消费上账 message为空");
            return;
        }
        if (FundAccountSubType.isBusinessAccount(iMessage.getFundAcctSubType()) &&
                FundAccountModelType.isCredit(iMessage.getFundAcctSubModel())) {

            String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_FLOW_SYNC_BANK_KEY, iMessage.getFlowId());
            try {
                boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
                if (!lock) {
                    return;
                }
            } catch (InterruptedException e) {
                FinhubLogger.error("4.0【新账户系统异常】商务授信调用银行转账，尝试加锁异常", e);
                throw new FinPayException(REDIS_GET_LOCK_ERROR);
            }
            AcctBusinessCreditFlow committedFlow = acctBusinessCreditFlowService.queryAccountSubFlowByFlowId(iMessage.getFlowId());
            if (Objects.isNull(committedFlow)) {
                FinhubLogger.error("【kafka消费：商务授信上账流水为空 message：{}", JsonUtils.toJson(iMessage));
                return;
            }
            if (FundAcctSyncBankStatus.hasSyncNew(committedFlow.getSyncBankStatus())) {
                return;
            }
            AcctBusinessCredit accountByAccountId = acctBusinessCreditService.findAccountByAccountId(committedFlow.getAccountId());
            if (Objects.isNull(accountByAccountId)) {
                FinhubLogger.error("【kafka消费：商务授信上账流水对应账户为空 message：{}", JsonUtils.toJson(iMessage));
                return;
            }
            AcctBusinessCreditFlow updateFlow = new AcctBusinessCreditFlow();
            updateFlow.setId(accountByAccountId.getId());
            Integer callBackNum = committedFlow.getCallbackNum() + 1;
            if(callBackNum <=10) {
                updateFlow.setCallbackNum(callBackNum);
            }
            if (callBackNum > 1) {
                Double minutes = Math.pow(2, callBackNum);
                updateFlow.setCallbackNext(org.apache.commons.lang3.time.DateUtils.addMinutes(new Date(), minutes.intValue()));
            }
            try {
                BankTradeReqDto bankTradeReqDto = new BankTradeReqDto();
                bankTradeReqDto.setCompanyId(iMessage.getCompanyId());
                bankTradeReqDto.setOperationAmount(iMessage.getOperationAmount().abs().setScale(0, BigDecimal.ROUND_DOWN));
                bankTradeReqDto.setBankName(iMessage.getBankName());
                bankTradeReqDto.setPayAccountNo(iMessage.getPayAccountNo());
                bankTradeReqDto.setOperationUserId(iMessage.getOperationUserId());
                bankTradeReqDto.setTxnId(iMessage.getTxnId());
                bankTradeReqDto.setReceiveAccountNo(iMessage.getReceiveAccountNo());
                bankTradeReqDto.setSyncTrade(false);
                bankTradeReqDto.setAccountSubType(iMessage.getFundAcctSubType());
                bankTradeReqDto.setAccountModel(iMessage.getFundAcctSubModel());
                bankTradeReqDto.setAccountFlowId(iMessage.getFlowId());
                BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(iMessage.getReceiveAccountNo(), iMessage.getBankName());
                if(Objects.isNull(bankAcct)){
                    throw new FinPayException(ACCOUNT_RECEIPT_BANK_ERROR);
                }
                bankTradeReqDto.setReceiveAccountName(bankAcct.getCompanyMainName());
                /**
                 * 商务消费账户、授信模式、消费，设置为企业
                 * QX 2021-12-13 FBT-9351
                 */
                bankTradeReqDto.setCustomerType(CustomerTypeEnum.ENTERPRISE.getType());
                /**
                 * 商务消费账户、授信模式、消费，设置为企业
                 * QX 2021-12-13 FBT-9351
                 */
                bankTradeReqDto.setCustomerType(CustomerTypeEnum.ENTERPRISE.getType());
                CategoryTypeEnum categoryTypeEnum = CategoryTypeEnum.valueOf(committedFlow.getOrderType());
                /**
                 * 廊坊银行-商品名称-消费传入场景类型
                 */
                bankTradeReqDto.setGoods(categoryTypeEnum == null ? "" : categoryTypeEnum.getName());
                /**
                 * 廊坊银行-用途-消费传入场景类型
                 */
                bankTradeReqDto.setFunds(categoryTypeEnum == null ? "" : categoryTypeEnum.getName());
                FinhubLogger.info("iBankTradeService.bankConsume,参数:{}", JsonUtils.toJson(bankTradeReqDto));
                BankTradeRespDto bankTradeRespDto = iBankTradeService.bankConsume(bankTradeReqDto);
                FinhubLogger.info("iBankTradeService.bankConsume,返回:{}", JsonUtils.toJson(bankTradeRespDto));
            } catch (Exception ex) {
                FinhubLogger.error("【4.0账户系统异常】商务授信消费调用银行异常", ex);
            } finally {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.warn(REDIS_UN_LOCK_ERROR, e);
                }
            }
            acctBusinessCreditFlowService.updateByIdSelective(updateFlow);
        } else if (FundAccountSubType.isBusinessAccount(iMessage.getFundAcctSubType()) &&
                FundAccountModelType.isRecharge(iMessage.getFundAcctSubModel())) {

            String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_FLOW_SYNC_BANK_KEY, iMessage.getFlowId());
            try {
                boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
                if (!lock) {
                    return;
                }
            } catch (InterruptedException e) {
                FinhubLogger.error("4.0【新账户系统异常】商务充值调用银行转账，尝试加锁异常", e);
                throw new FinPayException(REDIS_GET_LOCK_ERROR);
            }
            AcctBusinessDebitFlow commitedFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(iMessage.getFlowId());
            if (FundAcctSyncBankStatus.hasSyncNew(commitedFlow.getSyncBankStatus())) {
                return;
            }
            AcctBusinessDebitFlow updateFlow = new AcctBusinessDebitFlow();
            updateFlow.setId(commitedFlow.getId());
            AcctBusinessDebit accountByAccountId = acctBusinessDebitService.findAccountByAccountId(commitedFlow.getAccountId());
            if (Objects.isNull(accountByAccountId)) {
                FinhubLogger.error("【kafka消费：商务授信上账流水对应账户为空 message：{}", JsonUtils.toJson(iMessage));
                return;
            }

            Integer callBankNum = commitedFlow.getCallbackNum() + 1;
            if (callBankNum <=10) {
                updateFlow.setCallbackNum(callBankNum);
            }
            if (callBankNum > 1) {
                Double minutes = Math.pow(2, callBankNum);
                updateFlow.setCallbackNext(org.apache.commons.lang3.time.DateUtils.addMinutes(new Date(), minutes.intValue()));
            }
            try {
                BankTradeReqDto bankTradeReqDto = new BankTradeReqDto();
                bankTradeReqDto.setCompanyId(iMessage.getCompanyId());
                bankTradeReqDto.setOperationAmount(iMessage.getOperationAmount().abs().setScale(0, BigDecimal.ROUND_DOWN));
                bankTradeReqDto.setBankName(iMessage.getBankName());
                bankTradeReqDto.setPayAccountNo(iMessage.getPayAccountNo());
                bankTradeReqDto.setOperationUserId(iMessage.getOperationUserId());
                bankTradeReqDto.setTxnId(iMessage.getTxnId());
                bankTradeReqDto.setReceiveAccountNo(iMessage.getReceiveAccountNo());
                bankTradeReqDto.setSyncTrade(false);
                bankTradeReqDto.setAccountSubType(iMessage.getFundAcctSubType());
                bankTradeReqDto.setAccountModel(iMessage.getFundAcctSubModel());
                bankTradeReqDto.setAccountFlowId(iMessage.getFlowId());
                BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(iMessage.getReceiveAccountNo(), iMessage.getBankName());
                if(Objects.isNull(bankAcct)){
                    throw new FinPayException(ACCOUNT_RECEIPT_BANK_ERROR);
                }
                bankTradeReqDto.setReceiveAccountName(bankAcct.getCompanyMainName());
                /**
                 * 商务消费账户、充值模式、消费，设置为企业
                 * QX 2021-12-13 FBT-9351
                 */
                bankTradeReqDto.setCustomerType(CustomerTypeEnum.ENTERPRISE.getType());
                CategoryTypeEnum categoryTypeEnum = CategoryTypeEnum.valueOf(commitedFlow.getOrderType());
                /**
                 * 廊坊银行-商品名称-消费传入场景类型
                 */
                bankTradeReqDto.setGoods(categoryTypeEnum == null ? "" : categoryTypeEnum.getName());
                /**
                 * 廊坊银行-用途-消费传入场景类型
                 */
                bankTradeReqDto.setFunds(categoryTypeEnum == null ? "" : categoryTypeEnum.getName());
                FinhubLogger.info("iBankTradeService.bankConsume,参数:{}", JsonUtils.toJson(bankTradeReqDto));
                BankTradeRespDto bankTradeRespDto = iBankTradeService.bankConsume(bankTradeReqDto);
                FinhubLogger.info("iBankTradeService.bankConsume,返回:{}", JsonUtils.toJson(bankTradeRespDto));
            } catch (Exception ex) {
                FinhubLogger.error("【4.0账户系统异常】商务充值退款调用银行转账异常", ex);
            } finally {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.warn(REDIS_UN_LOCK_ERROR, e);
                }
            }
            acctBusinessDebitFlowService.updateByIdSelective(updateFlow);
        }
    }

    @Override
    public void syncBankRefund(KafkaBankRefundTaskMsg iMessage) {
        if (Objects.isNull(iMessage)) {
            FinhubLogger.info("【kafka消费：因公退款发送消息:不需要处理】消费 data为空");
            return;
        }
        FinhubLogger.info("{}【kafka消息 ===因公消费退款上账消息信息接收", iMessage.getFlowId());
        if (FundAccountSubType.isBusinessAccount(iMessage.getFundAcctSubType()) &&
                FundAccountModelType.isCredit(iMessage.getFundAcctSubModel())) {
            String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_FLOW_SYNC_BANK_KEY, iMessage.getFlowId());
            try {
                boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
                if (!lock) {
                    return;
                }
            } catch (InterruptedException e) {
                FinhubLogger.error("4.0【新账户系统异常】商务授信退款调用银行转账，尝试加锁异常", e);
                throw new FinPayException(REDIS_GET_LOCK_ERROR);
            }
            AcctBusinessCreditFlow committedFlow = acctBusinessCreditFlowService.queryAccountSubFlowByFlowId(iMessage.getFlowId());
            if (FundAcctSyncBankStatus.hasSyncNew(committedFlow.getSyncBankStatus())) {
                return;
            }
            try {
                AcctBusinessCreditFlow consumeFlow = acctBusinessCreditFlowService.queryByTaskId(iMessage.getBizNo(), FundAcctDebitOptType.PUBLIC_CONSUME.getKey());
                if (Objects.isNull(consumeFlow) ||  !FundAcctSyncBankStatus.hasSyncNew(consumeFlow.getSyncBankStatus())) {
                    FinhubLogger.error("【4.0账户系统异常】商务授信退款调用银行转账异常,支付还未成功");
                    throw new FinPayException(GlobalResponseCode.BANK_CARD_PAY_REFUND_NOT_EXIST);
                }
                AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(consumeFlow.getCompanyId(), consumeFlow.getCompanyMainId(), consumeFlow.getBankName());
                if(Objects.isNull(acctCompanyMain)){
                    throw new FinPayException(MAIN_COMPANY_NO_EXIST);
                }
                BankTradeReqDto bankTradeReqDto = new BankTradeReqDto();
                bankTradeReqDto.setCompanyId(iMessage.getCompanyId());
                bankTradeReqDto.setOperationAmount(iMessage.getOperationAmount().abs().setScale(0, BigDecimal.ROUND_DOWN));
                bankTradeReqDto.setBankName(iMessage.getBankName());
                bankTradeReqDto.setPayAccountNo(iMessage.getPayAccountNo());
                bankTradeReqDto.setOperationUserId(iMessage.getOperationUserId());
                bankTradeReqDto.setTxnId(iMessage.getTxnId());
                bankTradeReqDto.setOrgSysOrdNo(iMessage.getOrgSysOrdNo());
                bankTradeReqDto.setReceiveAccountNo(iMessage.getReceiveAccountNo());
                bankTradeReqDto.setSyncTrade(false);
                bankTradeReqDto.setAccountSubType(iMessage.getFundAcctSubType());
                bankTradeReqDto.setAccountModel(iMessage.getFundAcctSubModel());
                bankTradeReqDto.setAccountFlowId(iMessage.getFlowId());
                bankTradeReqDto.setOrgSysOrdNo(consumeFlow.getBankTransNo());
                // 取银行企业名称
                bankTradeReqDto.setReceiveAccountName(StringUtils.isBlank(acctCompanyMain.getBankBusinessName()) ? acctCompanyMain.getBusinessName() : acctCompanyMain.getBankBusinessName());

                /**
                 * 商务消费账户、授信模式、退款，设置为企业
                 * QX 2021-12-13 FBT-9351
                 */
                bankTradeReqDto.setCustomerType(CustomerTypeEnum.ENTERPRISE.getType());
                FinhubLogger.info("iBankTradeService.bankRefund,参数:{}", JsonUtils.toJson(bankTradeReqDto));
                BankTradeRespDto bankTradeRespDto = iBankTradeService.bankRefund(bankTradeReqDto);
                FinhubLogger.info("iBankTradeService.bankRefund,返回：{}", JsonUtils.toJson(bankTradeRespDto));
            } catch (Exception ex) {
                FinhubLogger.error("【4.0账户系统异常】商务授信退款调用银行转账异常", ex);
            } finally {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.warn(REDIS_UN_LOCK_ERROR, e);
                }
            }
            acctBusinessCreditFlowService.updateCallBackById(committedFlow.getId(),committedFlow.getCallbackNum());
        } else if (FundAccountSubType.isBusinessAccount(iMessage.getFundAcctSubType()) &&
                FundAccountModelType.isRecharge(iMessage.getFundAcctSubModel())) {
            String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_FLOW_SYNC_BANK_KEY, iMessage.getFlowId());
            try {
                boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
                if (!lock) {
                    return;
                }
            } catch (InterruptedException e) {
                FinhubLogger.error("4.0【新账户系统异常】调用银行转账，尝试加锁异常", e);
                throw new FinPayException(REDIS_GET_LOCK_ERROR);
            }
            AcctBusinessDebitFlow committedFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(iMessage.getFlowId());

            if (FundAcctSyncBankStatus.hasSyncNew(committedFlow.getSyncBankStatus())) {
                return;
            }
            AcctBusinessDebitFlow consumeFlow = acctBusinessDebitFlowService.queryByTaskId(iMessage.getBizNo(), FundAcctDebitOptType.PUBLIC_CONSUME.getKey());
            if (Objects.isNull(consumeFlow) || !FundAcctSyncBankStatus.hasSyncNew(consumeFlow.getSyncBankStatus())) {
                FinhubLogger.warn("【4.0账户系统异常】商务授信退款调用银行转账异常,支付还未成功");
                return;
            }
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(consumeFlow.getCompanyId(), consumeFlow.getCompanyMainId(), consumeFlow.getBankName());
            if(Objects.isNull(acctCompanyMain)){
                throw new FinPayException(MAIN_COMPANY_NO_EXIST);
            }
            try {
                BankTradeReqDto bankTradeReqDto = new BankTradeReqDto();
                bankTradeReqDto.setCompanyId(iMessage.getCompanyId());
                bankTradeReqDto.setOperationAmount(iMessage.getOperationAmount().abs().setScale(0, BigDecimal.ROUND_DOWN));
                bankTradeReqDto.setBankName(iMessage.getBankName());
                bankTradeReqDto.setPayAccountNo(iMessage.getPayAccountNo());
                bankTradeReqDto.setOperationUserId(iMessage.getOperationUserId());
                bankTradeReqDto.setTxnId(iMessage.getTxnId());
                bankTradeReqDto.setOrgSysOrdNo(consumeFlow.getBankTransNo());
                bankTradeReqDto.setReceiveAccountNo(iMessage.getReceiveAccountNo());
                bankTradeReqDto.setSyncTrade(false);
                bankTradeReqDto.setAccountSubType(iMessage.getFundAcctSubType());
                bankTradeReqDto.setAccountModel(iMessage.getFundAcctSubModel());
                bankTradeReqDto.setAccountFlowId(iMessage.getFlowId());
                // 取银行企业名称
                bankTradeReqDto.setReceiveAccountName(StringUtils.isBlank(acctCompanyMain.getBankBusinessName()) ? acctCompanyMain.getBusinessName() : acctCompanyMain.getBankBusinessName());
                /**
                 * 商务消费账户、充值模式、退款，设置为企业
                 * QX 2021-12-13 FBT-9351
                 */
                bankTradeReqDto.setCustomerType(CustomerTypeEnum.ENTERPRISE.getType());
                FinhubLogger.info("iBankTradeService.bankRefund,参数:{}", JsonUtils.toJson(bankTradeReqDto));
                BankTradeRespDto bankTradeRespDto = iBankTradeService.bankRefund(bankTradeReqDto);
                FinhubLogger.info("iBankTradeService.bankRefund,返回：{}", JsonUtils.toJson(bankTradeRespDto));
            } catch (Exception ex) {
                FinhubLogger.error("【4.0账户系统异常】商务授信退款调用银行转账异常", ex);
            } finally {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.warn(REDIS_UN_LOCK_ERROR, e);
                }
            }
            acctBusinessDebitFlowService.updateCallBackById(committedFlow.getId(),committedFlow.getCallbackNum());
        }
    }

    @Override
    public boolean allAuthBeforeAcctCreate(List<CompanyPlatformAccountConvertDTO> companyPlatformAccountConvertDTOS, String companyId) {
        AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctComGwByComIdReqDTO.setCompanyId(companyId);
        List<AcctCommonBaseDTO> gatewayAccts = acctCompanyGatewayService.findActCommonByComId(acctComGwByComIdReqDTO);
        if(CollectionUtils.isEmpty(gatewayAccts)){
            return false;
        }
        List<AcctCommonBaseDTO>  authBeforeCommonBase = new ArrayList<>();
        List<AcctCommonBaseDTO>  authBeforeCommonBasePublic = new ArrayList<>();
        //uc开户权限转换为网关数据结构，方便后续对比，判断是否可以将前置权限配置设置为失效
        companyPlatformAccountConvertDTOS.stream().forEach(companyPlatformAccountConvertDTO->{
            if(Objects.equals(FundPlatformEnum.FBT.getKey(),companyPlatformAccountConvertDTO.getPlatformSide())){
                makeAcctCommonBaseDTO(companyPlatformAccountConvertDTO,BankNameEnum.FBT.getCode(),authBeforeCommonBase,authBeforeCommonBasePublic);
            }else if(Objects.equals(FundPlatformEnum.ZBANK.getKey(),companyPlatformAccountConvertDTO.getPlatformSide())){
                makeAcctCommonBaseDTO(companyPlatformAccountConvertDTO,BankNameEnum.ZBBANK.getCode(),authBeforeCommonBase,authBeforeCommonBasePublic);
            }else if(Objects.equals(FundPlatformEnum.CITIC.getKey(),companyPlatformAccountConvertDTO.getPlatformSide())){
                makeAcctCommonBaseDTO(companyPlatformAccountConvertDTO,BankNameEnum.CITIC.getCode(),authBeforeCommonBase,authBeforeCommonBasePublic);
                makeAcctCommonBaseDTO4AcctReimbursement(companyPlatformAccountConvertDTO,BankNameEnum.CITIC.getCode(),authBeforeCommonBasePublic);
            }
        });

        //如果不为空，说明还有未创建或者未激活的账户，前置配置不可以失效；否则前置配置可以失效
        authBeforeCommonBase.removeAll(gatewayAccts);
        if(CollectionUtils.isNotEmpty(authBeforeCommonBase)){
            return false;
        }

        //对公账户是否全部创建完成
        if(CollectionUtils.isNotEmpty(authBeforeCommonBasePublic)){
            List<AccountPublic> accountPublics = acctPublicDechService.queryAcctPublicByCompanyId(companyId);
            //无对公账户，权限前置配置中选择了权限，说明还未创建对公账户，不可以置前置权限为失效
            if(CollectionUtils.isEmpty(accountPublics)){
                return false;
            }
            //权限前置中已经选择的对公账户，还有未创建的，则不可以将前置权限置为失效
            Map<String, List<AcctCommonBaseDTO>> commonBaseAuthBefore = authBeforeCommonBasePublic.stream().collect(Collectors.groupingBy(AcctCommonBaseDTO::getBankName));
            Set<String> commonBaseAuthBeforeBankName = commonBaseAuthBefore.keySet();
            Map<String, List<AccountPublic>> acctCreate = accountPublics.stream().filter(e->FundAcctDirectAcctTypeEnum.isBank(e.getDirectAcctType())).collect(Collectors.groupingBy(AccountPublic::getBankAccountName));
            Set<String> acctCreateBankName = acctCreate.keySet();
            commonBaseAuthBeforeBankName.removeAll(acctCreateBankName);
            if(CollectionUtils.isNotEmpty(commonBaseAuthBeforeBankName)){
                return false;
            }
        }
        return true;
    }

    @Override
    public List<AcctNeedReminderRespDTO> transferOut2OthersActivInfo(AcctTransferDebitReqDTO reqDTO) {
        List<AcctNeedReminderRespDTO> acctNeedReminderRespDTOS = new ArrayList<>();
        //得到转账资金下的开户平台权限
        List<AcctCompanyMain> byMainIds = acctCompanyMainService.findByMainIds(Arrays.asList(reqDTO.getCompanyMainId()));
        if(CollectionUtils.isEmpty(byMainIds)){
            return acctNeedReminderRespDTOS;
        }
        FundPlatformEnum platformByName = FundPlatformEnum.findPlatFrom(reqDTO.getBankName());
        CompanyPlatformAccountOperateDTO rechargeDTO = new CompanyPlatformAccountOperateDTO();
        rechargeDTO.setAccountHolder(byMainIds.get(0).getCompanyMainType());
        rechargeDTO.setCompanyId(reqDTO.getCompanyId());
        rechargeDTO.setAccountType(RECHARGE.getKey());
        rechargeDTO.setPlatformSide(platformByName.getKey());
        CompanyPlatformAccountConvertDTO companyPlatformAccount = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
        //权限格式转换 uc存储格式转换为pay的格式。
        List<AcctCommonBaseDTO>  authBeforeCommonBase = new ArrayList<>();
        List<AcctCommonBaseDTO>  authBeforeCommonBasePublic = new ArrayList<>();
        makeAcctCommonBaseDTO(companyPlatformAccount,reqDTO.getBankName(),authBeforeCommonBase,authBeforeCommonBasePublic);
        //没有配置前置权限，返回
        if(CollectionUtils.isEmpty(authBeforeCommonBase)){
            return acctNeedReminderRespDTOS;
        }
        //需要提示的账户类型，前置权限中有，请求参数中有
        List<AcctCommonBaseDTO> needReminder = new ArrayList<>();
        for(AcctTransferBaseInfoDTO acctTransferBaseInfoDTO:reqDTO.getTransferList()){
            for(AcctCommonBaseDTO acctCommonBaseDTO : authBeforeCommonBase){
                if(Objects.equals(acctTransferBaseInfoDTO.getAccountSubType(),acctCommonBaseDTO.getAccountSubType())
                        && Objects.equals(reqDTO.getBankName(),acctCommonBaseDTO.getBankName())
                        && FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel()) ){
                          acctCommonBaseDTO.setAccountId(acctTransferBaseInfoDTO.getAccountId());
                          needReminder.add(acctCommonBaseDTO);
                          break;
                }
            }
        }
        if(CollectionUtils.isEmpty(needReminder)){
            return acctNeedReminderRespDTOS;
        }
        AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctComGwByComIdReqDTO.setCompanyId(reqDTO.getCompanyId());
        List<AcctCommonBaseDTO> currActiviAcct = acctCompanyGatewayService.findActCommonByComId(acctComGwByComIdReqDTO);
        for(AcctCommonBaseDTO acctCommonBaseDTO : currActiviAcct){
            for(AcctCommonBaseDTO needReminderAcctCommonBaseDTO: needReminder){
                if(Objects.equals(acctCommonBaseDTO.getAccountSubType(),needReminderAcctCommonBaseDTO.getAccountSubType())
                && Objects.equals(acctCommonBaseDTO.getAccountModel(),needReminderAcctCommonBaseDTO.getAccountModel())
                && !Objects.equals(acctCommonBaseDTO.getBankName(),needReminderAcctCommonBaseDTO.getBankName())){
                    //组装返回体，提示信息
                    AcctNeedReminderRespDTO acctNeedReminderRespDTO = new AcctNeedReminderRespDTO();
                    acctNeedReminderRespDTO.setCompanyId(acctCommonBaseDTO.getCompanyId());
                    acctNeedReminderRespDTO.setAcctSubType(acctCommonBaseDTO.getAccountSubType());
                    acctNeedReminderRespDTO.setAcctSubTypeDesc(FundAccountSubType.getEnum(acctCommonBaseDTO.getAccountSubType()).getValue());
                    AcctNeedReminderSwitchRespDTO originalAcct = new AcctNeedReminderSwitchRespDTO();
                    originalAcct.setAccountId(acctCommonBaseDTO.getAccountId());
                    originalAcct.setAcctountType(acctCommonBaseDTO.getAccountSubType());
                    originalAcct.setAcctountModel(acctCommonBaseDTO.getAccountModel());
                    originalAcct.setBankName(acctCommonBaseDTO.getBankName());
                    getDesc(acctCommonBaseDTO.getAccountSubType(), acctCommonBaseDTO.getAccountId(), acctCommonBaseDTO.getBankName(),originalAcct);
                    acctNeedReminderRespDTO.setOriginalAcct(originalAcct);
                    AcctNeedReminderSwitchRespDTO destinationAcct = new AcctNeedReminderSwitchRespDTO();
                    destinationAcct.setAcctountModel(needReminderAcctCommonBaseDTO.getAccountModel());
                    destinationAcct.setAcctountType(needReminderAcctCommonBaseDTO.getAccountSubType());
                    destinationAcct.setAccountId(needReminderAcctCommonBaseDTO.getAccountId());
                    destinationAcct.setBankName(needReminderAcctCommonBaseDTO.getBankName());
                    getDesc(needReminderAcctCommonBaseDTO.getAccountSubType(), needReminderAcctCommonBaseDTO.getAccountId(), needReminderAcctCommonBaseDTO.getBankName(),destinationAcct);
                    acctNeedReminderRespDTO.setDestinationAcct(destinationAcct);
                    acctNeedReminderRespDTOS.add(acctNeedReminderRespDTO);
                    break;
                }
            }
        }
        return acctNeedReminderRespDTOS;
    }

    @Override
    public void changeAcctActiviteStatus(List<AcctNeedReminderRespDTO> needActivity) {
        if(CollectionUtils.isNotEmpty(needActivity)){
            for(AcctNeedReminderRespDTO acctNeedReminderRespDTO : needActivity){
                //激活的置位非激活，移出网关
                if(FundAccountSubType.isBusinessAccount(acctNeedReminderRespDTO.getOriginalAcct().getAcctountType())){
                    AcctBusinessDebit accountByAcctId = uAcctBusinessDebitService.findAccountByAcctId(acctNeedReminderRespDTO.getOriginalAcct().getAccountId());
                    AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                    acctUpdateCommonReqDTO.setAccountId(accountByAcctId.getAccountId());
                    uAcctBusinessDebitService.unActivateAcctBuDebitSub(acctUpdateCommonReqDTO);

                    AcctBusinessDebit destnationAcct = uAcctBusinessDebitService.findAccountByAcctId(acctNeedReminderRespDTO.getDestinationAcct().getAccountId());
                    AcctUpdateCommonReqDTO acctUpdateCommonReqDTO1 = new AcctUpdateCommonReqDTO();
                    acctUpdateCommonReqDTO1.setAccountId(destnationAcct.getAccountId());
                    uAcctBusinessDebitService.activateAcctBuDebitSub(acctUpdateCommonReqDTO1);
                    //网关更改信息
                    AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
                    BeanUtils.copyProperties(destnationAcct,acctComGwUpdateReqDTO);
                    acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
                }else if(FundAccountSubType.isIndividualAccount(acctNeedReminderRespDTO.getOriginalAcct().getAcctountType())){
                    AcctIndividualDebit originalAcct = uAcctIndividualDebitService.findAccountByAcctId(acctNeedReminderRespDTO.getOriginalAcct().getAccountId());
                    AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                    acctUpdateCommonReqDTO.setAccountId(originalAcct.getAccountId());
                    uAcctIndividualDebitService.unActivateAcctInDebitSub(acctUpdateCommonReqDTO);

                    AcctIndividualDebit destnationAcct = uAcctIndividualDebitService.findAccountByAcctId(acctNeedReminderRespDTO.getDestinationAcct().getAccountId());
                    acctUpdateCommonReqDTO.setAccountId(destnationAcct.getAccountId());
                    uAcctIndividualDebitService.activateAcctInDebitSub(acctUpdateCommonReqDTO);
                    //网关更改信息
                    AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
                    BeanUtils.copyProperties(destnationAcct,acctComGwUpdateReqDTO);
                    acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
                }else if(FundAccountSubType.isComCardAccount(acctNeedReminderRespDTO.getOriginalAcct().getAcctountType())){
                    AcctUpdateCommonReqDTO  acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                    acctUpdateCommonReqDTO.setAccountId(acctNeedReminderRespDTO.getDestinationAcct().getAccountId());
                    acctUpdateCommonReqDTO.setCompanyId(acctNeedReminderRespDTO.getCompanyId());
                    uAcctCompanyCardService.activateCompanyCardSub(acctUpdateCommonReqDTO);
                    AcctCompanyCard acctCompanyCard = uAcctCompanyCardService.findAcctCompanyCardByAcctId(acctNeedReminderRespDTO.getDestinationAcct().getAccountId());
                    //网关更改信息
                    AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
                    BeanUtils.copyProperties(acctCompanyCard,acctComGwUpdateReqDTO);
                    acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
                }
                //需要激活的激活，加入网关
            }
        }
    }

    /**
     * 根据UC前置配置权限构建生效子账户
     * @param companyPlatformAccountConvertDTO
     * @param bankName 开户银行
     * @param authBeforeCommonBase 前置权限
     * @param authBeforeCommonBasePublic 对公付款前置权限
     */
    private void makeAcctCommonBaseDTO(CompanyPlatformAccountConvertDTO companyPlatformAccountConvertDTO,String bankName,List<AcctCommonBaseDTO>  authBeforeCommonBase,List<AcctCommonBaseDTO>  authBeforeCommonBasePublic) {
        Integer acctActiviStatusBusiness = companyPlatformAccountConvertDTO.getBusinessConsume();
        if(FundAcctStatusEnum.isEnable(acctActiviStatusBusiness) ){
            AcctCommonBaseDTO acctCommonBaseDTO = new AcctCommonBaseDTO();
            acctCommonBaseDTO.setBankName(bankName);
            //模式
            Integer accountModel = Objects.nonNull(companyPlatformAccountConvertDTO.getAccountType()) ? companyPlatformAccountConvertDTO.getAccountType() : FundAccountModelType.RECHARGE.getKey();
            acctCommonBaseDTO.setAccountModel(accountModel);
            acctCommonBaseDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            authBeforeCommonBase.add(acctCommonBaseDTO);
        }
        Integer acctActiviStatusPerson = companyPlatformAccountConvertDTO.getPersonalConsume();
        if(FundAcctStatusEnum.isEnable(acctActiviStatusPerson)){
            AcctCommonBaseDTO acctCommonBaseDTO = new AcctCommonBaseDTO();
            acctCommonBaseDTO.setBankName(bankName);
            //模式
            Integer accountModel = Objects.nonNull(companyPlatformAccountConvertDTO.getAccountType()) ? companyPlatformAccountConvertDTO.getAccountType() : FundAccountModelType.RECHARGE.getKey();
            acctCommonBaseDTO.setAccountModel(accountModel);
            acctCommonBaseDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            authBeforeCommonBase.add(acctCommonBaseDTO);
        }
        Integer acctActiviStatusVirCard = companyPlatformAccountConvertDTO.getVirtualCard();
        if(FundAcctStatusEnum.isEnable(acctActiviStatusVirCard)){
            AcctCommonBaseDTO acctCommonBaseDTO = new AcctCommonBaseDTO();
            acctCommonBaseDTO.setBankName(bankName);
            //模式
            Integer accountModel = Objects.nonNull(companyPlatformAccountConvertDTO.getAccountType()) ? companyPlatformAccountConvertDTO.getAccountType() : FundAccountModelType.RECHARGE.getKey();
            acctCommonBaseDTO.setAccountModel(accountModel);
            acctCommonBaseDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
            authBeforeCommonBase.add(acctCommonBaseDTO);
        }
        Integer corporatePayment = companyPlatformAccountConvertDTO.getCorporatePayment();
        if(FundAcctStatusEnum.isEnable(corporatePayment)){
            AcctCommonBaseDTO acctCommonBaseDTO = new AcctCommonBaseDTO();
            acctCommonBaseDTO.setBankName(bankName);
            //模式
            Integer accountModel = Objects.nonNull(companyPlatformAccountConvertDTO.getAccountType()) ? companyPlatformAccountConvertDTO.getAccountType() : FundAccountModelType.RECHARGE.getKey();
            acctCommonBaseDTO.setAccountModel(accountModel);
            acctCommonBaseDTO.setAccountSubType(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey());
            authBeforeCommonBasePublic.add(acctCommonBaseDTO);
        }
    }

    /**
     * 根据UC前置配置权限构建生效子账户
     * @param companyPlatformAccountConvertDTO
     * @param bankName 开户银行
     * @param authBeforeCommonBasePublic 对公付款前置权限
     */
    private void makeAcctCommonBaseDTO4AcctReimbursement(CompanyPlatformAccountConvertDTO companyPlatformAccountConvertDTO,String bankName,List<AcctCommonBaseDTO>  authBeforeCommonBasePublic) {
        Integer acctReimbursement = companyPlatformAccountConvertDTO.getEmployeeReimburse();
        if(acctReimbursement!= null && FundAcctStatusEnum.isEnable(acctReimbursement)){
            AcctCommonBaseDTO acctCommonBaseDTO = new AcctCommonBaseDTO();
            acctCommonBaseDTO.setBankName(bankName);
            //模式
            Integer accountModel = Objects.nonNull(companyPlatformAccountConvertDTO.getAccountType()) ? companyPlatformAccountConvertDTO.getAccountType() : FundAccountModelType.RECHARGE.getKey();
            acctCommonBaseDTO.setAccountModel(accountModel);
            acctCommonBaseDTO.setAccountSubType(FundAccountSubType.REIMBURSEMENT_ACCOUNT.getKey());
            authBeforeCommonBasePublic.add(acctCommonBaseDTO);
        }
    }

    private void getDesc(Integer accountSubType,String accountId,String bankName,AcctNeedReminderSwitchRespDTO originalAcct){
        if(FundAccountSubType.isBusinessAccount(accountSubType)){
            AcctBusinessDebit acctBusinessDebit = acctBusinessDebitService.findAccountByAccountId(accountId);
            BankNameEnum bankNameEnum = BankNameEnum.getBankEnum(bankName);
            FundAccountSubType accountSubTypeEnum = FundAccountSubType.getEnum(accountSubType);
            originalAcct.setPlateName(bankNameEnum.getName() + bankNameEnum.getPlateDesc());
            originalAcct.setAcctountTypeName(accountSubTypeEnum.getValue());
            originalAcct.setBalance(BigDecimalUtils.fen2yuan(acctBusinessDebit.getBalance()));
            originalAcct.setIcon(bankNameEnum.getBankIconWebBig());
        } else if(FundAccountSubType.isIndividualAccount(accountSubType)){
            AcctIndividualDebit acctIndividualDebit = acctIndividualDebitService.findByAcctId(accountId);
            BankNameEnum bankNameEnum = BankNameEnum.getBankEnum(bankName);
            FundAccountSubType accountSubTypeEnum = FundAccountSubType.getEnum(accountSubType);
            originalAcct.setPlateName(bankNameEnum.getName() + bankNameEnum.getPlateDesc());
            originalAcct.setAcctountTypeName(accountSubTypeEnum.getValue());
            originalAcct.setBalance(BigDecimalUtils.fen2yuan(acctIndividualDebit.getBalance()));
            originalAcct.setIcon(bankNameEnum.getBankIconWebBig());

        }else if(FundAccountSubType.isComCardAccount(accountSubType)){
            AcctCompanyCard acctCompanyCard = acctCompanyCardService.queryByAccountId(accountId);
            BankNameEnum bankNameEnum = BankNameEnum.getBankEnum(bankName);
            FundAccountSubType accountSubTypeEnum = FundAccountSubType.getEnum(accountSubType);
            originalAcct.setPlateName(bankNameEnum.getName() + bankNameEnum.getPlateDesc());
            originalAcct.setAcctountTypeName(accountSubTypeEnum.getValue());
            originalAcct.setBalance(BigDecimalUtils.fen2yuan(acctCompanyCard.getBalance()));
            originalAcct.setIcon(bankNameEnum.getBankIconWebBig());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AcctBalanceAdjustRespDTO acctBalanceAdjust(AcctBalanceAdjustReqDTO acctBalanceAdjustReqDTO) {
        AcctBalanceAdjustRespDTO acctBalanceAdjustRespDTO = new AcctBalanceAdjustRespDTO();
        String rechargeResult = acctBalanceAdjust0(acctBalanceAdjustReqDTO);
        acctBalanceAdjustRespDTO.setFlowId(rechargeResult);
        return acctBalanceAdjustRespDTO;
    }

    /**
     * Propagation.REQUIRED：如果当前存在事务，则加入该事务，如果当前不存在事务，则创建一个新的事务。( 也就是说如果A方法和B方法都添加了注解，在默认传播模式下，A方法内部调用B方法，会把两个方法的事务合并为一个事务 ）
     * @param fromAcct 调减账户
     * @param toAcct 调增账户
     * @return Boolean
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AcctBalanceAdjustRespDTO acctBalanceAdjust(AcctBalanceAdjustReqDTO fromAcct, AcctBalanceAdjustReqDTO toAcct) {
        AcctBalanceAdjustRespDTO acctBalanceAdjustRespDTO = new AcctBalanceAdjustRespDTO();
        String rechargeResult = acctBalanceAdjust0(fromAcct);
        acctBalanceAdjustRespDTO.setFromFlowId(rechargeResult);
        String toRechargeResult = acctBalanceAdjust0(toAcct);
        acctBalanceAdjustRespDTO.setToFlowId(toRechargeResult);
        return acctBalanceAdjustRespDTO;
    }

    @Override
    public List<AcctGeneralSpaRespDTO> spaAcctList(String companyId) {
        List<AccountGeneral> accountGenerals =  uAcctGeneralService.findByCompanyId(companyId);
        List<AcctGeneralSpaRespDTO> acctGeneralSpaRespDTOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(accountGenerals)){
            for(AccountGeneral accountGeneral: accountGenerals){
                if (BankNameEnum.isSpa(accountGeneral.getBankName())){
                    AcctGeneralSpaRespDTO acctGeneralSpaRespDTO = new AcctGeneralSpaRespDTO();
                    BeanUtils.copyProperties(accountGeneral,acctGeneralSpaRespDTO);
                    acctGeneralSpaRespDTO.setBankMainShowName(BankNameShowConfig.makeBankMainShowName(accountGeneral.getBankName(),accountGeneral.getBankAccountNo(),accountGeneral.getAccountModel()));
                    acctGeneralSpaRespDTOS.add(acctGeneralSpaRespDTO);
                }
            }
        }
        return acctGeneralSpaRespDTOS;
    }

    @Override
    public boolean updateAcctContract(String contractUrl, String bankName, String bankAcctNo) {
        FinhubLogger.info("【更新电子合同】参数：{},{}", contractUrl, bankName, bankAcctNo);
        AccountGeneral byBank = uAcctGeneralService.findByBank(bankName, bankAcctNo);
        if(Objects.isNull(byBank)){
            FinhubLogger.info("【更新电子合同】未查询都余额账户信息，参数：{},{}", contractUrl, bankName, bankAcctNo);
            return false;
        }
        AccountGeneral updateAcct = new AccountGeneral();
        updateAcct.setId(byBank.getId());
        updateAcct.setBankContractUrl(contractUrl);
        return uAcctGeneralService.updateAcctContract(updateAcct);
    }

    @Override
    public void updatePublicAcctByBank(AcctCreateBankReqDTO createBankReqDTO) {
        AccountPublic accountPublic = acctPublicSearchService.queryByCompanyIdAndBankAccountNo(createBankReqDTO.getCompanyId(), createBankReqDTO.getBankAccountNo());
        if (Objects.isNull(accountPublic)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_PUBLIC_NO_EXIST);
        }
        AccountPublic update = new AccountPublic();
        update.setId(accountPublic.getId());
        update.setBankAccountAcctName(createBankReqDTO.getAcctCreateMainReqDTO().getBusinessName());
        int i = acctPublicDechService.updateAcctPublicById(update);

        // 同步费控
        CompletableFuture.runAsync(() -> {
            accountInfoUpdateKafka.delayedSendAccountInfoUpdateMsg(accountPublic.getCompanyId(), accountPublic.getAccountPublicId(), null, AccountOptTypeEnum.UPDATE.getCode());
        });
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public boolean zxUpdateAcctMain(String bankName, String bankAcctNo) {
        FinhubLogger.info("【中信账户升级】参数：{},{}", bankName, bankAcctNo);
        AccountGeneral byBank = uAcctGeneralService.findByBank(bankName, bankAcctNo);
        if(Objects.isNull(byBank)){
            FinhubLogger.warn("【中信账户升级】未查询到余额账户信息，参数：{},{}", bankName, bankAcctNo);
            return false;
        }
        AcctCompanyMain findAcctCompanyMain = acctCompanyMainService.findAcctCompanyMain(byBank.getCompanyId(), byBank.getCompanyMainId(), byBank.getBankName());
        if(Objects.isNull(findAcctCompanyMain)){
            FinhubLogger.warn("【中信账户升级】未查询账户主体信息，参数：{},{}", bankName, bankAcctNo);
            return false;
        }
        AcctCompanyMain acctCompanyMain = new AcctCompanyMain();
        acctCompanyMain.setId(findAcctCompanyMain.getId());
        acctCompanyMain.setBankBusinessName("北京分贝通科技有限公司待结算户-" + findAcctCompanyMain.getBusinessName());
        acctCompanyMain.setUpdateTime(new Date());
        int i = acctCompanyMainService.updateAcctCompanyMain(acctCompanyMain);
        String key = ZX_UPDATE_REDIS_KEY + byBank.getCompanyId() + "_" + byBank.getBankAccountNo();
        try {
            redisDao.getRedisTemplate().opsForValue().set(key, JsonUtils.toJson(acctCompanyMain));
            return i > 0 ;
        } catch (Exception e) {
            FinhubLogger.error("【中信账户升级】设置缓存异常，参数：{},{}", bankName, bankAcctNo, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public ZxAccountUpgradeWindowShowVo upgradeWindowShow(String companyId) {
        ZxAccountUpgradeWindowShowVo result = new ZxAccountUpgradeWindowShowVo();
        List<ZxAccountUpgradeInfoVo> list = new ArrayList<>();
        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
        // 没有账户列表-不弹
        if (CollectionUtils.isEmpty(accountGenerals)) {
            FinhubLogger.warn("【中信账户升级弹窗】未查询到账户列表，参数：companId={}", companyId);
            return result;
        }
        // 没有中信账户列表-不弹
        List<AccountGeneral> citicAccounts = accountGenerals.stream().filter(s -> BankNameEnum.isCitic(s.getBankName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(citicAccounts)) {
            FinhubLogger.warn("【中信账户升级弹窗】未查询到中信账户，参数：companId={}", companyId);
            return result;
        }
        for (AccountGeneral citicAccount : citicAccounts) {
            String key = ZX_UPDATE_REDIS_KEY + citicAccount.getCompanyId() + "_" + citicAccount.getBankAccountNo();
            Object upgradeInfo = redisDao.getRedisTemplate().opsForValue().get(key);
            FinhubLogger.info("【中信账户升级弹窗】升级账户信息={}，缓存信息={}", companyId);
            if (Objects.nonNull(upgradeInfo)) {
                AcctCompanyMain acctCompanyMain = JsonUtils.toObj(upgradeInfo.toString(), AcctCompanyMain.class);
                ZxAccountUpgradeInfoVo zxAccountUpgradeInfoVo = new ZxAccountUpgradeInfoVo();
                zxAccountUpgradeInfoVo.setUpgradeAccountNo(citicAccount.getBankAccountNo());
                zxAccountUpgradeInfoVo.setUpgradeAccountName(citicAccount.getCompanyMainName());
                zxAccountUpgradeInfoVo.setUpgradeTime(DateUtils.format(acctCompanyMain.getUpdateTime(), DateUtils.FORMAT_DATETIME_CHAR));
                zxAccountUpgradeInfoVo.setUpgradeAfterAccountName(acctCompanyMain.getBankBusinessName());
                list.add(zxAccountUpgradeInfoVo);
                result.setWindowShow(true);
            }
        }
        result.setInfoVoList(list);
        return result;
    }

    @Override
    public void upgradeWindowConfirm(String companyId) {
        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
        if (CollectionUtils.isEmpty(accountGenerals)) {
            FinhubLogger.error("【中信账户升级弹窗确认】未查询到账户列表，参数：companId={}", companyId);
            return ;
        }
        List<AccountGeneral> citicAccounts = accountGenerals.stream().filter(s -> BankNameEnum.isCitic(s.getBankName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(citicAccounts)) {
            FinhubLogger.error("【中信账户升级弹窗确认】未查询到中信账户，参数：companId={}", companyId);
            return ;
        }
        for (AccountGeneral citicAccount : citicAccounts) {
            String key = ZX_UPDATE_REDIS_KEY + citicAccount.getCompanyId() + "_" + citicAccount.getBankAccountNo();
            Object upgradeInfo = redisDao.getRedisTemplate().opsForValue().get(key);
            FinhubLogger.info("【中信账户升级弹窗】升级账户信息={}，缓存信息={}", companyId);
            if (Objects.nonNull(upgradeInfo)) {
                redisDao.getRedisTemplate().delete(key);
            }
        }
    }
    
    @Override
    public List<CompanyAccountInfo> queryGroupAcctAuth(GroupAcctReqDTO reqDTO) {
    	return employeeService.getCompanyAccountWithAuth(reqDTO);
    }

    /**
     * 集团版 获取有权限公司、账户列表
     * <AUTHOR>
     * @date 2022-07-05 19:15:11
     */
    @Override
    public List<CompanyAccountInfo> queryGroupAcctAuthList(GroupAcctReqDTO reqDTO) {
        // 查询账户权限 注意：companyId uc 不用来作为过滤条件
        List<CompanyAccountInfo> companyAccountInfos = employeeService.getCompanyAccountAuth(reqDTO);
        return queryAuthGroupAcctList(companyAccountInfos);
    }

    @Override
    public void changeOpenAccountInfoMainType(AccountGeneral mainGeneral, AccountChangeNameReqDTO changeNameReqDTO, CompanyMainTypeEnum companyMainTypeEnum) {
        BankPublicAccountInfoReqDTO bankPublicAccountInfoReqDTO = new BankPublicAccountInfoReqDTO();
        bankPublicAccountInfoReqDTO.setBankCode(mainGeneral.getBankName());
        bankPublicAccountInfoReqDTO.setBankAcctId(mainGeneral.getBankAcctId());
        bankPublicAccountInfoReqDTO.setCompanyMainType(companyMainTypeEnum.getKey());
        bankPublicAccountInfoReqDTO.setCompanyName(changeNameReqDTO.getCompanyName());
        Boolean updateByBankCodeAndBankAcctId = iBankPublicAccountInfoService.updateByBankCodeAndBankAcctId(bankPublicAccountInfoReqDTO);
        FinhubLogger.info("【新账户系统修改企业核心信息】修改开户记录主体类型 mainGeneral：{}, 修改结果：{}", JsonUtils.toJson(mainGeneral), updateByBankCodeAndBankAcctId);
    }

    @Override
    public void changeAccountMainType(AccountGeneral selfGeneral, CompanyMainTypeEnum companyMainTypeEnum) {
        //校验字段
        boolean resultGen = uAcctGeneralService.changeMainTypeByAccount(selfGeneral.getCompanyId(), selfGeneral.getBankName(), selfGeneral.getBankAccountNo(), companyMainTypeEnum.getKey());
        boolean resultBusDebit = uAcctBusinessDebitService.changeMainTypeByAccount(selfGeneral.getCompanyId(), selfGeneral.getBankName(), selfGeneral.getBankAccountNo(), companyMainTypeEnum.getKey());
        boolean resultInvDebit = uAcctIndividualDebitService.changeMainTypeByAccount(selfGeneral.getCompanyId(), selfGeneral.getBankName(), selfGeneral.getBankAccountNo(), companyMainTypeEnum.getKey());
        boolean companyCard = uAcctCompanyCardService.changeMainTypeByAccount(selfGeneral.getCompanyId(), selfGeneral.getBankName(), selfGeneral.getBankAccountNo(), companyMainTypeEnum.getKey());
        boolean acctPublic = acctPublicService.changeMainTypeByAccount(selfGeneral.getCompanyId(), selfGeneral.getBankName(), selfGeneral.getBankAccountNo(), companyMainTypeEnum.getKey());
        boolean acctReimbursement = acctReimbursementService.changeMainTypeByAccount(selfGeneral.getCompanyId(), selfGeneral.getBankName(), selfGeneral.getBankAccountNo(), companyMainTypeEnum.getKey());
        boolean changeCompanyMainNameFlag = uAcctCompanyMainService.changeMainTypeByMainId(selfGeneral.getCompanyId(), selfGeneral.getCompanyMainId(), companyMainTypeEnum.getKey());

        boolean isAllSuc = resultGen && changeCompanyMainNameFlag
                && resultBusDebit && resultInvDebit
                && companyCard && acctPublic
                && acctReimbursement;
        if(!isAllSuc){
            String msg = "账户主体类型变更,余额："+resultGen + ",主体:" + changeCompanyMainNameFlag
                    + ",商务充值:" + resultBusDebit + ",个人充值:" + resultInvDebit + ",虚拟卡充值:" + companyCard
                    + ",对公:" + acctPublic + ",报销:" + acctReimbursement;
            FinhubLogger.error("UC账户主体类型变更失败 selfGeneral = {}：", JsonUtils.toJson(selfGeneral));
            dingDingMsgService.sendMsg(msg + "," + selfGeneral.getCompanyId() + "," + selfGeneral.getCompanyName() );
        }
    }

    @Override
    public void otherMainCreateBusIndCardAccount(AccountGeneral selfGeneral) {
        String companyId = selfGeneral.getCompanyId();
        String bankName = selfGeneral.getBankName();
        String bankAcctNo = selfGeneral.getBankAccountNo();
        AcctCreateSubDTO acctCreateSubDTO = buildCreateAccountParam(selfGeneral);
        // 商务
        AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(companyId, bankName, bankAcctNo);
        if (Objects.isNull(acctBusinessDebit)) {
            uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
        }
        // 个人
        AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(companyId, bankName, bankAcctNo);
        if (Objects.isNull(acctIndividualDebit)) {
            uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
        }
        // 虚拟卡
        AcctCompanyCard acctCompanyCard = uAcctCompanyCardService.findByCompanyIdAndBank(companyId, bankName, bankAcctNo);
        if (Objects.isNull(acctCompanyCard)) {
            acctCreateSubDTO.setBankChannelName(selfGeneral.getBankName());
            uAcctCompanyCardService.createAccount(acctCreateSubDTO, 1);
        }
    }

    @Override
    public List<AcctBaseMainRespDTO> queryMainAcctView4CompanyCard(AcctOverviewReqDTO reqDTO) {
        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(reqDTO.getCompanyId());
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return Lists.newArrayList();
        }
        List<AcctBaseMainRespDTO> acctBaseMainRespDTOS = Lists.newArrayList();
        // TODO 排序规则
        //电子账户的虚拟卡账户
        List<AcctCompanyCard> acctCompanyCards = acctCompanyCardService.findByCompanyId(reqDTO.getCompanyId());
        if (CollectionUtils.isNotEmpty(acctCompanyCards)){
            for (AcctCompanyCard acctCompanyCard:acctCompanyCards) {
                    buildAcctBaseMainRespDTO4CompanyCard(accountGenerals,acctBaseMainRespDTOS,acctCompanyCard.getCompanyMainId());
            }
        }
        //无论如何都填充分贝通充值/授信账户
        if (acctBaseMainRespDTOS.size() == 0){
            buildDefaultAcctBaseMainRespDTO4CompanyCard(accountGenerals,acctBaseMainRespDTOS,"none");
        }
        return acctBaseMainRespDTOS;
    }

    @Override
    public AcctCompanyMainDataRespDTO queryMainOnly(AcctCompanyMainDataReqDTO reqDTO) {
        AcctCompanyMainDataRespDTO respDTO = new AcctCompanyMainDataRespDTO();
        AccountGeneral accountGeneral = uAcctGeneralService.findByCompanyIdAndBank(reqDTO.getCompanyId(), reqDTO.getBankName(), reqDTO.getBankAccountNo());
        if (Objects.isNull(accountGeneral)) {
            FinhubLogger.error("【查询主体信息】未查询到账户信息 reqDTO={}", JsonUtils.toJson(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
        }
        // 查询主体信息
        AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.findAcctCompanyMain(accountGeneral.getCompanyId(), accountGeneral.getCompanyMainId(), accountGeneral.getBankName());
        if (Objects.isNull(acctCompanyMain)) {
            FinhubLogger.error("【查询主体信息】其他主体未查询到主体信息 accountGeneral 参数：{}", JsonUtils.toJson(accountGeneral));
            throw new FinPayException(GlobalResponseCode.MAIN_COMPANY_NO_EXIST);
        }
        BeanUtils.copyProperties(acctCompanyMain, respDTO);
        return respDTO;
    }

    @Override
    public String getShowMainName(String bankName, String companyMainName, String bankAccountNo, AcctCompanyMain acctCompanyMain) {
        /**
         * ZHIFU-5070 中信账户名称优化
         * 1.上线之后展示主体名称
         * 2.白名单内展示主体名称
         */
        if (!BankNameEnum.isCitic(bankName)) {
            return companyMainName;
        }
        if (Objects.nonNull(acctCompanyMain)) {
            Date setUpDate = DateUtils.toDate("2023-02-10", DateUtils.FORMAT_DATE_PATTERN);
            Object zxShowMainNameWhiteList = redisDao.getRedisTemplate().opsForValue().get(ZX_SHOW_MAIN_NAME_WHITE_LIST_REDIS_KEY);
            if (DateUtils.compareByDate(acctCompanyMain.getCreateTime(), setUpDate) > 0 || (Objects.nonNull(zxShowMainNameWhiteList) && zxShowMainNameWhiteList.toString().contains(bankAccountNo))) {
                return acctCompanyMain.getBusinessName();
            }else {
                if (StringUtils.isNotEmpty(acctCompanyMain.getBankBusinessName())) {
                   return acctCompanyMain.getBankBusinessName();
                } else {
                    return acctCompanyMain.getBusinessName();
                }
            }
        }
        return companyMainName;
    }

    public void buildDefaultAcctBaseMainRespDTO4CompanyCard(List<AccountGeneral> accountGenerals,List<AcctBaseMainRespDTO> target,String companyMainId){
        for (AccountGeneral accountGeneral : accountGenerals) {
            if (FundPlatformEnum.isFBTPlatform(accountGeneral.getBankName())) {
                //只添加一次
                if (accountGeneral.getCompanyMainId().equals(companyMainId)){
                    AcctBaseMainRespDTO acctBaseMainRespDTO = new AcctBaseMainRespDTO();
                    BeanUtils.copyProperties(accountGeneral, acctBaseMainRespDTO);
                    acctBaseMainRespDTO.setAccountId(accountGeneral.getAccountGeneralId());
                    acctBaseMainRespDTO.setAcctWebSelectKey(accountGeneral.getAccountGeneralId());
                    // 返回是否下载交易电子回单
                    acctBaseMainRespDTO.setDownloadTradeFlow(accountGeneral.getBankName());
                    target.add(acctBaseMainRespDTO);
                }
                //拷贝一个分贝通的的授信帐户下拉平台卡
                if (FundAccountModelType.isRecharge(accountGeneral.getAccountModel())) {
                    AcctBusinessCredit businessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    AcctIndividualCredit individualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    if ((Objects.nonNull(businessCredit) && FundAcctShowStatusEnum.isShow(businessCredit.getShowStatus()))
                            || (Objects.nonNull(individualCredit) && FundAcctShowStatusEnum.isShow(individualCredit.getShowStatus()))) {
                        AcctBaseMainRespDTO copayAcctFbt = new AcctBaseMainRespDTO();
                        BeanUtils.copyProperties(accountGeneral, copayAcctFbt);
                        copayAcctFbt.setAccountId(accountGeneral.getAccountGeneralId());
                        copayAcctFbt.setAcctWebSelectKey(accountGeneral.getAccountGeneralId() + CREDIT.getKey());
                        copayAcctFbt.setAccountModel(CREDIT.getKey());
                        target.add(copayAcctFbt);
                    }
                }
                //拷贝一个分贝通的的充值帐户下拉平台卡
                if (FundAccountModelType.isCredit(accountGeneral.getAccountModel())) {
                    //查询
                    AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    AcctIndividualDebit companyIdAndBank = uAcctIndividualDebitService.findCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                    if ((Objects.nonNull(acctBusinessDebit) && FundAcctShowStatusEnum.isShow(acctBusinessDebit.getShowStatus()))
                            || (Objects.nonNull(companyIdAndBank) && FundAcctShowStatusEnum.isShow(companyIdAndBank.getShowStatus()))) {
                        AcctBaseMainRespDTO copayAcctFbt = new AcctBaseMainRespDTO();
                        BeanUtils.copyProperties(accountGeneral, copayAcctFbt);
                        copayAcctFbt.setAccountId(accountGeneral.getAccountGeneralId());
                        copayAcctFbt.setAcctWebSelectKey(accountGeneral.getAccountGeneralId() + RECHARGE.getKey());
                        copayAcctFbt.setAccountModel(RECHARGE.getKey());
                        target.add(copayAcctFbt);
                    }
                }
            }
        }
    }
    public void buildAcctBaseMainRespDTO4CompanyCard(List<AccountGeneral> accountGenerals,List<AcctBaseMainRespDTO> target,String companyMainId){
        //如果企业从来没有虚拟卡账户,则默认返回一条当前的账户
        for (AccountGeneral accountGeneral : accountGenerals) {
            if (accountGeneral.getCompanyMainId().equals(companyMainId)) {
                AcctBaseMainRespDTO acctBaseMainRespDTO = new AcctBaseMainRespDTO();
                BeanUtils.copyProperties(accountGeneral, acctBaseMainRespDTO);
                acctBaseMainRespDTO.setAccountId(accountGeneral.getAccountGeneralId());
                acctBaseMainRespDTO.setAcctWebSelectKey(accountGeneral.getAccountGeneralId());
                // 返回是否下载交易电子回单
                acctBaseMainRespDTO.setDownloadTradeFlow(accountGeneral.getBankName());
                target.add(acctBaseMainRespDTO);
            }
        }
    }

    private AcctCreateSubDTO buildCreateAccountParam(AccountGeneral selfGeneral) {
        AcctCreateSubDTO acctCreateSubDTO = new AcctCreateSubDTO();
        BeanUtils.copyProperties(selfGeneral, acctCreateSubDTO);
        acctCreateSubDTO.setAccountGeneralId(selfGeneral.getAccountGeneralId());
        acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
        acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.UN_ABLE.getStatus());
        acctCreateSubDTO.setShowStatus(SHOW.getStatus());
        return acctCreateSubDTO;
    }

    //=============================Private Method======================

    private String acctBalanceAdjust0(AcctBalanceAdjustReqDTO acctBalanceAdjustReqDTO) {
        if(FundAcctGeneralOptType.ADJUST_RECHARGE.getKey() ==  acctBalanceAdjustReqDTO.getOperationType()){
            //通用方法的返回值先不改,还用exception的方式处理
            AccountGeneralFlow  accountGeneralFlow = uAcctGeneralService.acctBalanceAdjustRecharge(acctBalanceAdjustReqDTO);
            return accountGeneralFlow.getAccountFlowId();
        }
        if(FundAcctGeneralOptType.ADJUST_WITHDRAWAL.getKey() ==  acctBalanceAdjustReqDTO.getOperationType()){
            AccountGeneralFlow  accountGeneralFlow =  uAcctGeneralService.acctBalanceAdjustWithdrawal(acctBalanceAdjustReqDTO);
            return accountGeneralFlow.getAccountFlowId();
        }
        return null;
    }

    /**
     * 创建网银付账户
     * @param createBankReqDTO
     * @param platformAccountListDTO
     * @param acctCreateSubDTO
     */
    private void createAcctPublic(AcctCreateBankReqDTO createBankReqDTO, CompanyPlatformAccountConvertDTO platformAccountListDTO, AcctCreateSubDTO acctCreateSubDTO) {
        Integer publicPay = platformAccountListDTO.getCorporatePayment();
        if (FundAcctStatusEnum.isKnow(publicPay)) {
            acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.isEnable(publicPay) ? AccountPublicStatus.NORMAL.getKey() : AccountPublicStatus.DISABLE.getKey());
            acctCreateSubDTO.setShowStatus(FundAcctStatusEnum.isEnable(publicPay) ? SHOW.getStatus() : UN_SHOW.getStatus());
            AcctPublicCreateReqDTO reqDTO = new AcctPublicCreateReqDTO();
            BeanUtils.copyProperties(acctCreateSubDTO, reqDTO);
            BeanUtils.copyProperties(createBankReqDTO, reqDTO);
            AcctCreateMainReqDTO acctCreateMainReqDTO = createBankReqDTO.getAcctCreateMainReqDTO();
            BeanUtils.copyProperties(acctCreateMainReqDTO, reqDTO);

            reqDTO.setBankCardName(acctCreateMainReqDTO.getBankCardName());
            reqDTO.setBankCardNo(acctCreateMainReqDTO.getBankCardNo());
            reqDTO.setBankAccountAcctName(acctCreateMainReqDTO.getBusinessName());
            reqDTO.setCompanyAccountId(acctCreateMainReqDTO.getCompanyAccountId());

            reqDTO.setCompanyMainId(createBankReqDTO.getCompanyMainId());
            reqDTO.setCompanyMainType(createBankReqDTO.getCompanyMainType());
            reqDTO.setBankName(createBankReqDTO.getBankName());

            reqDTO.setOperationChannel(OperationChannelType.WEB.getKey());
            reqDTO.setBizNo(acctCreateSubDTO.getBankAccountNo());
            reqDTO.setBankAcctId(acctCreateSubDTO.getBankAcctId());
            reqDTO.setAccountModel(acctCreateSubDTO.getAccountModel());

            if (BankNameEnum.isZBBank(createBankReqDTO.getBankName())) {
                reqDTO.setBankAccountPic(zbFbtContractUrl);
            } else if (BankNameEnum.isCitic(createBankReqDTO.getBankName())) {
                reqDTO.setBankAccountPic(citicFbtContractUrl);
            }
            if (BankNameEnum.isSpa(createBankReqDTO.getBankName())) {
                reqDTO.setBankAccountPic(spaFbtContractUrl);
            }
            if (BankNameEnum.isLfBank(createBankReqDTO.getBankName())) {
                reqDTO.setBankAccountPic(lfbankFbtContractUrl);
            }
            acctPublicDechService.createAcctPublic(reqDTO);
        }
    }

    /**
     * 创建企业虚拟卡账户
     * @param createBankReqDTO
     * @param platformAccountListDTO
     * @param acctCreateSubDTO
     */
    private void createAcctCompanyCard(AcctCreateBankReqDTO createBankReqDTO, CompanyPlatformAccountConvertDTO platformAccountListDTO, AcctCreateSubDTO acctCreateSubDTO) {
        FinhubLogger.info("createAcctCompanyCard createBankReqDTO ={}", JsonUtils.toJson(createBankReqDTO));
        FinhubLogger.info("createAcctCompanyCard platformAccountListDTO={}", JsonUtils.toJson(platformAccountListDTO));
        FinhubLogger.info("createAcctCompanyCard acctCreateSubDTO={}", JsonUtils.toJson(acctCreateSubDTO));
        //虚拟卡
        Integer virtualCard = platformAccountListDTO.getVirtualCard();
        if (FundAcctStatusEnum.isKnow(virtualCard)) {
            acctCreateSubDTO.setAccountStatus(virtualCard);
            acctCreateSubDTO.setShowStatus(FundAcctStatusEnum.isEnable(virtualCard) ? SHOW.getStatus() : UN_SHOW.getStatus());
            acctCreateSubDTO.setBankChannelName(createBankReqDTO.getBankName());
            // flag=1 广发  flag=2 FBT
            uAcctCompanyCardService.createAccount(acctCreateSubDTO, 1);

            //如果是前置配置
            //判断当前账户是否选中，如果没有选中，则直接创建账户；否则，需要判断激活后的逻辑。
            //判断当前账户是否可以激活，如果可以激活需要激活，否则不激活
            //如果激活，需要判断是否通知uc将配置文件失效
            if(Objects.equals(EffectiveAccountSourceEnum.NORMAL.getKey(), platformAccountListDTO.getSource()) && FundAcctStatusEnum.isEnable(virtualCard)) {
                AcctCompanyCard acctCompanyCard = uAcctCompanyCardService.findByCompanyIdAndBank(acctCreateSubDTO.getCompanyId(), acctCreateSubDTO.getBankName(), acctCreateSubDTO.getBankAccountNo());
                FinhubLogger.info("createAcctCompanyCard findByCompanyIdAndBank={}", JsonUtils.toJson(acctCompanyCard));

                AcctComGwByAcctTypeReqDTO acctComGwByAcctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
                acctComGwByAcctTypeReqDTO.setCompanyId(acctCreateSubDTO.getCompanyId());
                acctComGwByAcctTypeReqDTO.setFundAccountType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
                AcctCommonBaseDTO actCommonByAcctType = acctCompanyGatewayService.findActCommonByAcctType(acctComGwByAcctTypeReqDTO);
                //TODO 一人多卡要改造
                //当前类型的账户无激活的账户，创建并且激活
                if(Objects.isNull(actCommonByAcctType)){
                    //设置为激活状态
                    AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                    acctUpdateCommonReqDTO.setAccountId(acctCompanyCard.getAccountId());
                    uAcctCompanyCardService.activateCompanyCardSub(acctUpdateCommonReqDTO);
                    //加入网关
                    AcctCompanyGateway acctCompanyGateway = new AcctCompanyGateway();
                    BeanUtils.copyProperties(acctCompanyCard,acctCompanyGateway);
                    acctCompanyGatewayService.addActGws(acctCompanyGateway);
                }else {
                    //当前类型账户有激活账户，查看该账户下是否有流水，无流水，激活，加入网关，原账户改为不激活，网关中移除;否则，不激活
                    int flowCount = acctBusinessDebitFlowService.queryHaveFlow(actCommonByAcctType.getAccountId());
                    if(flowCount == 0){
                        //设置为激活状态
                        AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                        acctUpdateCommonReqDTO.setAccountId(acctCompanyCard.getAccountId());
                        acctUpdateCommonReqDTO.setCompanyId(acctCompanyCard.getCompanyId());
                        uAcctCompanyCardService.activateCompanyCardSub(acctUpdateCommonReqDTO);
                        //更新新网关，当前创建的加入到网关
                        AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
                        BeanUtils.copyProperties(acctCompanyCard,acctComGwUpdateReqDTO);
                        acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
                    }
                }
            }
        }
    }

    /**
     * 创建个人账户
     * @param platformAccountListDTO
     * @param acctCreateSubDTO
     */
    private void createAcctIndividual(CompanyPlatformAccountConvertDTO platformAccountListDTO, AcctCreateSubDTO acctCreateSubDTO) {
        //个人
        Integer personalConsume = platformAccountListDTO.getPersonalConsume();
        if (FundAcctStatusEnum.isKnow(personalConsume)) {
            acctCreateSubDTO.setAccountStatus(personalConsume);
            acctCreateSubDTO.setShowStatus(FundAcctStatusEnum.isEnable(personalConsume) ? SHOW.getStatus() : UN_SHOW.getStatus());
            uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);

            AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(acctCreateSubDTO.getCompanyId(), acctCreateSubDTO.getBankName(), acctCreateSubDTO.getBankAccountNo());
            //如果是前置配置
            //判断当前账户是否选中，如果没有选中，则直接创建账户；否则，需要判断激活后的逻辑。
            //判断当前账户是否可以激活，如果可以激活需要激活，否则不激活
            //如果激活，需要判断是否通知uc将配置文件失效
            if(Objects.equals(EffectiveAccountSourceEnum.NORMAL.getKey(), platformAccountListDTO.getSource()) && FundAcctStatusEnum.isEnable(personalConsume)) {
                AcctComGwByAcctTypeReqDTO acctComGwByAcctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
                acctComGwByAcctTypeReqDTO.setCompanyId(acctCreateSubDTO.getCompanyId());
                acctComGwByAcctTypeReqDTO.setFundAccountType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                AcctCommonBaseDTO actCommonByAcctType = acctCompanyGatewayService.findActCommonByAcctType(acctComGwByAcctTypeReqDTO);
                //当前类型的账户无激活的账户，创建并且激活
                if(Objects.isNull(actCommonByAcctType)){
                    //设置为激活状态
                    AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                    acctUpdateCommonReqDTO.setAccountId(acctIndividualDebit.getAccountId());
                    uAcctIndividualDebitService.activateAcctInDebitSub(acctUpdateCommonReqDTO);
                    //加入网关
                    AcctCompanyGateway acctCompanyGateway = new AcctCompanyGateway();
                    BeanUtils.copyProperties(acctIndividualDebit,acctCompanyGateway);
                    acctCompanyGatewayService.addActGws(acctCompanyGateway);
                }else {
                    //当前类型账户有激活账户，查看该账户下是否有流水，无流水，激活，加入网关，原账户改为不激活，网关中移除;否则，不激活
                    int flowCount = acctIndividualDebitFlowService.queryHaveFlow(actCommonByAcctType.getAccountId());
                    if(flowCount == 0){
                        //设置为激活状态
                        AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                        acctUpdateCommonReqDTO.setAccountId(acctIndividualDebit.getAccountId());
                        uAcctIndividualDebitService.activateAcctInDebitSub(acctUpdateCommonReqDTO);
                        //更新新网关，当前创建的加入到网关
                        AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
                        BeanUtils.copyProperties(acctIndividualDebit,acctComGwUpdateReqDTO);
                        acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
                        //当前生效的置位失效
                        AcctUpdateCommonReqDTO updateCommonReqDTO = new AcctUpdateCommonReqDTO();
                        updateCommonReqDTO.setAccountId(actCommonByAcctType.getAccountId());
                        uAcctIndividualDebitService.unActivateAcctInDebitSub(updateCommonReqDTO);
                    }
                }
            }
        }
    }
    
    /**
     * 创建海外卡账户
     * @param platformAccountList 企业平台开户权限
     * @param acctCreateSub 子账户创建DTO
     */
    private void createAcctOversea(CompanyPlatformAccountConvertDTO platformAccountList, AcctCreateSubDTO acctCreateSub) {
        Integer overseaAcct = platformAccountList.getOverSeaEnterpriseCard();
        if (Objects.isNull(overseaAcct) || !FundAcctActStatusEnum.isAct(overseaAcct)) {
        	return;
        }

        acctCreateSub.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
        acctCreateSub.setShowStatus(SHOW.getStatus());
        acctCreateSub.setActiveStatus(FundAcctActStatusEnum.ACTIVATE.getStatus());
        AcctOversea acct = acctOverseaService.creatAcctOversea(acctCreateSub);

        AcctComGwByAcctTypeReqDTO acctComGwByAcctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctComGwByAcctTypeReqDTO.setCompanyId(acctCreateSub.getCompanyId());
        acctComGwByAcctTypeReqDTO.setFundAccountType(FundAccountSubType.OVERSEA_ACCOUNT.getKey());
        AcctCommonBaseDTO actCommonByAcctType = acctCompanyGatewayService.findActCommonByAcctType(acctComGwByAcctTypeReqDTO);
        
        if(Objects.isNull(actCommonByAcctType)){
            //加入网关
            AcctCompanyGateway acctCompanyGateway = new AcctCompanyGateway();
            BeanUtils.copyProperties(acct, acctCompanyGateway);
            acctCompanyGatewayService.addActGws(acctCompanyGateway);
        } else {
        	//更新新网关，当前创建的加入到网关
            AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
            BeanUtils.copyProperties(acct, acctComGwUpdateReqDTO);
            acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
        }
    }

    /**
     * 创建商务账户
     * @param platformAccountListDTO 企业平台开户权限
     * @param acctCreateSubDTO 子账户创建DTO
     */
    private void createAcctBusiness(CompanyPlatformAccountConvertDTO platformAccountListDTO, AcctCreateSubDTO acctCreateSubDTO) {
        //商务
        Integer businessConsume = platformAccountListDTO.getBusinessConsume();
        if (FundAcctStatusEnum.isKnow(businessConsume)) {
            acctCreateSubDTO.setAccountStatus(businessConsume);
            acctCreateSubDTO.setShowStatus(FundAcctStatusEnum.isEnable(businessConsume) ? SHOW.getStatus() : UN_SHOW.getStatus());
            uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
            AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(acctCreateSubDTO.getCompanyId(), acctCreateSubDTO.getBankName(), acctCreateSubDTO.getBankAccountNo());
            //如果是前置配置
            //判断当前账户是否选中，如果没有选中，则直接创建账户；否则，需要判断激活后的逻辑。
            //判断当前账户是否可以激活，如果可以激活需要激活，否则不激活
            //如果激活，需要判断是否通知uc将配置文件失效
            if(Objects.equals(EffectiveAccountSourceEnum.NORMAL.getKey(), platformAccountListDTO.getSource()) && FundAcctStatusEnum.isEnable(businessConsume)) {
                AcctComGwByAcctTypeReqDTO acctComGwByAcctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
                acctComGwByAcctTypeReqDTO.setCompanyId(acctCreateSubDTO.getCompanyId());
                acctComGwByAcctTypeReqDTO.setFundAccountType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                AcctCommonBaseDTO actCommonByAcctType = acctCompanyGatewayService.findActCommonByAcctType(acctComGwByAcctTypeReqDTO);
                //当前类型的账户无激活的账户，创建并且激活
                if(Objects.isNull(actCommonByAcctType)){
                    //设置为激活状态
                    AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                    acctUpdateCommonReqDTO.setAccountId(acctBusinessDebit.getAccountId());
                    uAcctBusinessDebitService.activateAcctBuDebitSub(acctUpdateCommonReqDTO);
                    //加入网关
                    AcctCompanyGateway acctCompanyGateway = new AcctCompanyGateway();
                    BeanUtils.copyProperties(acctBusinessDebit,acctCompanyGateway);
                    acctCompanyGatewayService.addActGws(acctCompanyGateway);
                }else {
                    //当前类型账户有激活账户，查看该账户下是否有流水，无流水，激活，加入网关，原账户改为不激活，网关中移除;否则，不激活
                    int flowCount = acctBusinessDebitFlowService.queryHaveFlow(actCommonByAcctType.getAccountId());
                    if(flowCount == 0){
                        //设置为激活状态
                        AcctUpdateCommonReqDTO acctUpdateCommonReqDTO = new AcctUpdateCommonReqDTO();
                        acctUpdateCommonReqDTO.setAccountId(acctBusinessDebit.getAccountId());
                        uAcctBusinessDebitService.activateAcctBuDebitSub(acctUpdateCommonReqDTO);
                        //更新新网关，当前创建的加入到网关
                        AcctComGwUpdateReqDTO acctComGwUpdateReqDTO = new AcctComGwUpdateReqDTO();
                        BeanUtils.copyProperties(acctBusinessDebit,acctComGwUpdateReqDTO);
                        acctCompanyGatewayService.updateActGw(acctComGwUpdateReqDTO);
                        //当前生效的置位失效
                        AcctUpdateCommonReqDTO updateCommonReqDTO = new AcctUpdateCommonReqDTO();
                        updateCommonReqDTO.setAccountId(actCommonByAcctType.getAccountId());
                        uAcctBusinessDebitService.unActivateAcctBuDebitSub(updateCommonReqDTO);
                    }
                }
            }
        }
    }
    
    @Override
    public void checkIfCompanyHaveOtherEntityAccount(String companyId) {
    	List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
    	if (CollectionUtils.isEmpty(accountGenerals)) {
    		FinhubLogger.warn("no account exists companyId->{}", companyId);
    		return;
    	}
    	
        List<AcctPublicDetailRespDTO> publicRespDtos = acctPublicSearchService.findByCompanyId(companyId);
        doCheck(publicRespDtos, false);
    }
    
    private void doCheck(List<?> list, boolean toEnable) {
    	Optional.ofNullable(list).orElse(Collections.emptyList())
    	.stream()
    	.forEach(acct -> checkIfAccounIsOtherEntity(acct, toEnable));
    }
    
    @Override
    public void checkIfCompanyHaveOtherEntityAccount(String companyId, String accountId) {
    	List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(companyId);
    	
    	if (CollectionUtils.isEmpty(accountGenerals)) {
    		FinhubLogger.warn("OtherEntity:no account exists companyId->{}", companyId);
    		return;
    	}
    	        
        List<AcctPublicDetailRespDTO> publicRespDtos = acctPublicSearchService.findByCompanyId(companyId);
        publicRespDtos = Optional.ofNullable(publicRespDtos).orElse(Collections.emptyList())
        		.stream().filter(acct -> Objects.equals(accountId, acct.getAccountId())).collect(Collectors.toList());
        doCheck(publicRespDtos, true);
    }
    
    private void checkIfAccounIsOtherEntity(Object account, boolean toEnable) {
    	if (Objects.isNull(account)) {
    		return;
    	}
    	
    	try {
    		boolean isToB = false;
    		Class<?> clazz = account.getClass();
    		if (clazz == AcctPublicDetailRespDTO.class) {
    			clazz = clazz.getSuperclass();
    			isToB = true;
    		}
			Field field = clazz.getDeclaredField("companyMainType");
			field.setAccessible(true);
			Field acctField = clazz.getDeclaredField("accountStatus");
			acctField.setAccessible(true);
			Integer entityType = (Integer)field.get(account);
			Integer accountStatus = (Integer)acctField.get(account);
			if (Objects.equals(CompanyMainTypeEnum.COMPANY_MAIN_OTHER.getKey(), entityType) && 
					((Objects.equals(0, accountStatus) && !isToB) || (isToB && Objects.equals(toEnable ? 3 : 2, accountStatus)))) {
				throw new FinPayException(GlobalResponseCode.GROUP_COMPANY_HAS_OTHER_ENTITY);
			}
		} catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e) {
			FinhubLogger.warn("error when checkIfAccounIsOtherEntity->", e);
		}
    	
    }
    
    /**
     * 创建员工报销账户
     */
    public void createAcctReimbursement(AcctCreateBankReqDTO createBankReqDTO, CompanyPlatformAccountConvertDTO platformAccountListDTO, AcctCreateSubDTO acctCreateSubDTO){
        if (!BankNameEnum.isCitic(createBankReqDTO.getBankName()) && !BankNameEnum.isSpa(createBankReqDTO.getBankName())&& !BankNameEnum.isSpd(createBankReqDTO.getBankName())){
            return;
        }
        Integer acctReimbursement = platformAccountListDTO.getEmployeeReimburse();
        if (acctReimbursement!= null && FundAcctStatusEnum.isKnow(acctReimbursement)) {
            acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.isEnable(acctReimbursement) ? FundAcctStatusEnum.ENABLE.getStatus() : FundAcctStatusEnum.UN_ABLE.getStatus());
            acctCreateSubDTO.setShowStatus(FundAcctStatusEnum.isEnable(acctReimbursement) ? SHOW.getStatus() : UN_SHOW.getStatus());
            AcctCreateReimbursementReqDTO reqDTO = new AcctCreateReimbursementReqDTO();
            BeanUtils.copyProperties(acctCreateSubDTO, reqDTO);
            BeanUtils.copyProperties(createBankReqDTO, reqDTO);
            AcctCreateMainReqDTO acctCreateMainReqDTO = createBankReqDTO.getAcctCreateMainReqDTO();
            BeanUtils.copyProperties(acctCreateMainReqDTO, reqDTO);
            reqDTO.setBankCardName(acctCreateMainReqDTO.getBankCardName());
            reqDTO.setBankCardNo(acctCreateMainReqDTO.getBankCardNo());
            reqDTO.setBankAccountAcctName(acctCreateMainReqDTO.getBusinessName());
            reqDTO.setBankAcctId(acctCreateSubDTO.getBankAcctId());
            reqDTO.setCompanyAccountId(acctCreateMainReqDTO.getCompanyAccountId());
            reqDTO.setCompanyMainId(createBankReqDTO.getCompanyMainId());
            reqDTO.setCompanyMainType(createBankReqDTO.getCompanyMainType());
            reqDTO.setBankName(createBankReqDTO.getBankName());
            reqDTO.setOperationChannel(OperationChannelType.WEB.getKey());
            reqDTO.setBizNo(acctCreateSubDTO.getBankAccountNo());
            reqDTO.setAccountModel(acctCreateSubDTO.getAccountModel());
            reqDTO.setActiveStatus(ACTIVATE.getStatus());
            uAcctReimbursementService.establishAccount(reqDTO);
        }
    }

    /**
     * 构建授信主体
     * @author: zhaoxu
     * @date: 2022-06-03 13:38:43
     */
    private void buildAcctCreditMainList(List<AcctCreditMainRespDTO> acctCreditMainList, List<AcctBusinessCredit> businessCredits, List<AcctIndividualCredit> individualCredits, AccountGeneral general) {
        //授信主体
        if (!(CollectionUtils.isEmpty(businessCredits) && CollectionUtils.isEmpty(individualCredits))) {
            // 商务授信
            AcctBusinessCredit acctBusinessCredit = businessCredits.stream().filter(Objects::nonNull)
                    .filter(businessCredit -> general.getBankAccountNo().equalsIgnoreCase(businessCredit.getBankAccountNo())).findFirst().orElse(null);
            // 临时额度
            if (acctBusinessCredit != null) {
                AcctBusinessCreditRecoverTask businessTask = acctBusinessCreditRecoverTaskService.queryByAccountId(acctBusinessCredit.getAccountId());
                acctBusinessCredit.setRecoverDate(businessTask == null ? null : businessTask.getRecoverDate());
            }

            // 个人授信
            AcctIndividualCredit acctIndividualCredit = individualCredits.stream().filter(Objects::nonNull)
                    .filter(individualCredit -> general.getBankAccountNo().equalsIgnoreCase(individualCredit.getBankAccountNo())).findFirst().orElse(null);
            // 临时额度
            if (acctIndividualCredit != null) {
                AcctIndividualCreditRecoverTask individualTask = acctIndividualCreditRecoverTaskService.queryByAccountId(acctIndividualCredit.getAccountId());
                acctIndividualCredit.setRecoverDate(individualTask == null ? null : individualTask.getRecoverDate());
            }

            // 组装返回参数
            AcctCreditMainRespDTO creditMainRespDTO = AcctMainConvert.makeAcctCreditMainRespDTO(acctBusinessCredit, acctIndividualCredit, general);
            if (Objects.nonNull(creditMainRespDTO)) {
                acctCreditMainList.add(creditMainRespDTO);
            }
        }
    }

    /**
     * 根据权限查询集团账户信息
     * <AUTHOR>
     * @date 2022-07-07 13:50:04
     */
    private List<CompanyAccountInfo> queryAuthGroupAcctList(List<CompanyAccountInfo> infos) {
        // 查询公司下账户信息
        List<CompanyAccountInfo> result = new ArrayList<>();
        for (CompanyAccountInfo companyAccountInfo : infos) {
            CompanyAccountInfo info = queryAuthCompanyAcctList(companyAccountInfo);
            result.add(info);
        }
        FinhubLogger.info("根据权限查询集团账户信息queryAuthGroupAcctList result:{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 查询有权限账户
     * <AUTHOR>
     * @date 2022-07-07 13:51:14
     */
    private CompanyAccountInfo queryAuthCompanyAcctList(CompanyAccountInfo companyAccountInfo) {
        FinhubLogger.info("查询有权限账户queryAuthCompanyAcctList req:{}", JSON.toJSONString(companyAccountInfo));
        if (CompanyAccountInfo.AcctAuthEnum.NO_AUTH.equals(companyAccountInfo.getAcctAuthEnum())) {
            return companyAccountInfo;
        }
        List<AcctBaseMainRespDTO> acctList = this.queryMainAcctView(new AcctOverviewReqDTO(companyAccountInfo.getCompanyId()));
        FinhubLogger.info("查询有权限账户queryAuthCompanyAcctList queryMainAcctView req:{}", JSON.toJSONString(companyAccountInfo));
        List<AccountInfoVO> accountInfos = new ArrayList<>();
        List<FxCompanyAcctInfo> fxAccountInfos = new ArrayList<>();
        List<FxCompanyAcctInfos> fxCompanyAcctInfos = fxCompanyAcctBuild(companyAccountInfo.getCompanyId());
        // 公司所有权限
        if (CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA.equals(companyAccountInfo.getAcctAuthEnum())) {
            accountInfos = JSON.parseArray(JSON.toJSONString(acctList), AccountInfoVO.class);
            fxAccountInfos = JSON.parseArray(JSON.toJSONString(fxCompanyAcctInfos), FxCompanyAcctInfo.class);
        }

        // 有部分账户权限过滤
        if (CompanyAccountInfo.AcctAuthEnum.COMPANY_PART_DATA.equals(companyAccountInfo.getAcctAuthEnum())) {
            // 境内
            Map<String, AccountInfoVO> authMap = companyAccountInfo.getAccounts().stream().collect(Collectors.toMap(v -> v.getAccountId() + "_" + v.getAccountModel(), v -> v, (v, v1) -> v));
            List<AcctBaseMainRespDTO> authList = acctList.stream().filter(v -> (authMap.get(v.getAccountId() + "_" + v.getAccountModel()) != null)).collect(Collectors.toList());
            accountInfos = JSON.parseArray(JSON.toJSONString(authList), AccountInfoVO.class);

            // 海外卡
            List<FxCompanyAcctInfos> fxAuthList = fxCompanyAcctInfos.stream().filter(v -> (authMap.get(v.getAccountId() + "_" + CoreConstant.FX_UC_ACCOUNT_MODEL) != null)).collect(Collectors.toList());
            fxAccountInfos = JSON.parseArray(JSON.toJSONString(fxAuthList), FxCompanyAcctInfo.class);
        }
        companyAccountInfo.setAccounts(accountInfos);
        companyAccountInfo.setFxAccounts(fxAccountInfos);
        FinhubLogger.info("查询有权限账户queryAuthCompanyAcctList res:{}", JSON.toJSONString(companyAccountInfo));
        return companyAccountInfo;
    }

    private void sendAutoAcctCheckingMsg(KafkaDechTradeResultMsg iMessage, BankAcctFlow bankAcctFlow) {
        if (!AccountStatusEnum.isSuccess(iMessage.getTxnSt()) || Objects.isNull(bankAcctFlow)) {
            return;
        }
        BankAcctFlow flowMsg = bankAcctFlow;
        flowMsg.setSyncBankAmount(Objects.isNull(iMessage.getOperationAmount()) ? BigDecimal.ZERO : iMessage.getOperationAmount());
        flowMsg.setSyncBankTime(DateUtil.getDateFromStringByDefaultDateFormat(iMessage.getFinishTime()));
        flowMsg.setSyncBankTransNo(iMessage.getSyncBankTransNo());
        flowMsg.setBankTransNo(iMessage.getSysOrdNo());
        // 分贝券对账
        if ((BankNameEnum.isCgb(flowMsg.getBankName())||BankNameEnum.isSpa(flowMsg.getBankName())
                || BankNameEnum.isCitic(flowMsg.getBankName()) || BankNameEnum.isSpd(flowMsg.getBankName()))
                && BankAcctTypeEnum.isGuaranteeBankAccount(flowMsg.getBankAcctType())) {
            // 分贝券消费
            if (FundPlatAcctOptType.VOUCHER_CONSUME.getKey() == flowMsg.getOperationType()) {
                KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
                BeanUtils.copyProperties(flowMsg, kafkaAutoAcctCheckingMsg);
                kafkaAutoAcctCheckingMsg.setAccountSubType(FundAccountSubType.BANK_FREEN_ACCOUNT.getKey());
                kafkaAutoAcctCheckingMsg.setAccountFlowId(flowMsg.getBankAcctFlowId());
                kafkaAutoAcctCheckingMsg.setBizNo(flowMsg.getUnTxnId());
                autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
            }
            // 分贝券退款
            if (FundPlatAcctOptType.VOUCHER_REFUND.getKey() == flowMsg.getOperationType()) {
                KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
                BeanUtils.copyProperties(flowMsg, kafkaAutoAcctCheckingMsg);
                kafkaAutoAcctCheckingMsg.setAccountSubType(FundAccountSubType.BANK_FREEN_ACCOUNT.getKey());
                kafkaAutoAcctCheckingMsg.setAccountFlowId(flowMsg.getBankAcctFlowId());
                kafkaAutoAcctCheckingMsg.setBizNo(flowMsg.getUnReTxnId());
                autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
            }
        }
    }

    @Override
    public SpdBankTradeDownloadRespDTO spdBankTradeDown(SpdBankTradeDownLoadReqDTO dto,String userId,String userName) {
        SpdBankTradeDownloadRespDTO spdBankTradeDownloadRespDTO = new SpdBankTradeDownloadRespDTO();
        try{
            String fileName = getSpdExportFileName(dto);
            // 创建下载任务
            ExportElectronicTaskDTO exportDTO = new ExportElectronicTaskDTO(
                    dto.getTaskSrc(), fileName, RECEIPT_TASK_CATEGORY, RECEIPT_TASK_CATEGORY_NAME, 1, fileName,
                    userId, userName);
            String jsonString = JSON.toJSONString(exportDTO);
            FinhubLogger.info("【调用任务中心创建任务】请求参数：{}", jsonString);
            String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/outer/task", jsonString);
            FinhubLogger.info("【调用任务中心创建任务】返回参数：{}", postBody);
            String taskId = JSON.parseObject(postBody).getJSONObject("data").getString("taskId");
            spdBankTradeDownloadRespDTO.setTaskId(taskId);
            spdBankTradeDownloadRespDTO.setButtonText("下载到本地");
            // 打印操作 且数量不大于100 展示打印按钮
            if (MergePdfEnum.merge.getKey().equals(dto.getMergePdf())) {
                spdBankTradeDownloadRespDTO.setButtonText("打印");
            }
            // 异步下载
            CompletableFuture.runAsync(() -> {
            	PayContext.setUserId(Optional.ofNullable(userId).orElse("system"));
                downSpdLoadReceipt(dto,taskId,fileName);
                PayContext.clearConext();
            }, asyncExecutor);
        } catch (FinhubException fe) {
            FinhubLogger.warn("UAcctcommon#spdBankTradeDown#FinhubException#req:{}", JSON.toJSON(dto), fe);
            throw new FinhubException(fe.getCode(), fe.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("UAcctcommon#spdBankTradeDown#Exception#req:{}", JSON.toJSON(dto), e);
        }
        return spdBankTradeDownloadRespDTO;
    }

    private void downSpdLoadReceipt(SpdBankTradeDownLoadReqDTO dto, String taskId, String fileName) {
        // [默认1]执行状态:1 执行中
        int status = 1;
        String ossUrl = "";
        List<File> localFileList = new ArrayList<>();
        try {
            //根据传入时间，截取月份差，调用dech拿到电子回单
            Date beginDate = DateUtils.addMonth(dto.getBeginDate(), 1);
            Date endDate = DateUtils.addMonth(dto.getEndDate(), 1);
            while(DateUtils.compareByDate(beginDate,endDate)<=0){
                String yyyyMM = DateUtils.format(beginDate, "yyyyMM");
                String url = iSpdSearchService.queryYqdzDownLoadUrl(dto.getBankAccountNo(), yyyyMM + "01");
                FinhubLogger.info("iSpdSearchService.queryYqdzDownLoadUrl resp{}",url);
                // 电子回单状态扩展 流水状态扩展 下载状态也要扩展
                if (!StringUtils.isBlank(url)) {
                    // oss图片地址
                    String imageUrl = url;
                    // 本地文件路径名称
                    String pathName = genFileName("SPD_YQDZ");
                    // 从oss下载到本地文件
                    downOss2Local(imageUrl, pathName);
                    // 保存文件句柄
                    localFileList.add(new File(pathName));
                }
                beginDate=DateUtils.addMonth(beginDate, 1);
            }
            // 逐条上传
            if (MergePdfEnum.not_merge.getKey().equals(dto.getMergePdf())) {
                ossUrl = itemPdf(localFileList, fileName);
            } else {
                // 合并上传
                ossUrl = mergePdf(localFileList, fileName);
            }
            // 下载状态
            if (Objects.isNull(ossUrl)) {
                // 50 执行失败
                status = 50;
            } else {
                // 10 执行成功
                status = 10;
            }
        } catch (Exception e) {
            // 50 执行失败 出现异常
            status = 50;
            FinhubLogger.error("浦发对账单下载失败 taskId:" + taskId, e);
        } finally {
            // 通知下载结果
            noticeTaskProcess(status, fileName, taskId, ossUrl);
            // 删除文件
            if (CollectionUtils.isNotEmpty(localFileList)) {
                for (File srcFile : localFileList) {
                    srcFile.delete();
                }
            }
        }
    }

    private String getSpdExportFileName(SpdBankTradeDownLoadReqDTO dto) {
        StringBuilder sb = new StringBuilder();
        sb.append("浦发银行交易流水下载_");
        sb.append(DateUtils.format(dto.getBeginDate(),"yyyyMMdd"));
        sb.append("_");
        sb.append(DateUtils.format(dto.getEndDate(),"yyyyMMdd"));
        sb.append("_");
        sb.append(RandomUtil.randomNumbers(4));
        return sb.toString();
    }

    /**
     * 构建海外卡账户信息（目前只支持展示美元账户）
     * <AUTHOR>
     * @date 2023-04-23 14:31:37
     */
    private List<FxCompanyAcctInfos> fxCompanyAcctBuild(String companyId) {
        List<FxCompanyAcctInfos> fxCompanyAcctInfosList = new ArrayList<>();
        try {
            CompanyAcctInfoReq companyAcctInfoReq = new CompanyAcctInfoReq();
            companyAcctInfoReq.setCompanyId(companyId);
            companyAcctInfoReq.setAccountSubType(FxCompanyAccountSubType.PETTY);
            companyAcctInfoReq.setCurrency(CurrencyEnum.USD);
            // 备用金账户查询币种账户信息（目前仅有美元账户）
            ResponseVo<List<CompanyAcctRes>> res = iCompanyAcctService.queryCompanyAcctInfo(companyAcctInfoReq);

            // 待结算账户查询
            CompanyAcctInfoReq settlementReq = new CompanyAcctInfoReq();
            settlementReq.setCompanyId(companyId);
            settlementReq.setAccountSubType(FxCompanyAccountSubType.PENDING_SETTLEMENT);
            // 备用金账户查询币种账户信息（目前仅有美元账户）
            ResponseVo<List<CompanyAcctRes>> settlementRes = iCompanyAcctService.queryCompanyAcctInfo(settlementReq);
            List<CompanyAcctRes> acctRes = res.getData();
            List<CompanyAcctRes> settlementAcctRes = settlementRes.getData();
            if (CollectionUtils.isNotEmpty(acctRes)) {
                // 备用金账户按接入渠道分组计算
                Map<String, List<CompanyAcctRes>> channelInfoMap = acctRes.stream().collect(Collectors.groupingBy(CompanyAcctRes::getChannel));
                Map<String, List<CompanyAcctRes>> settlementAcctMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(settlementAcctRes)) {
                    settlementAcctMap = settlementAcctRes.stream().collect(Collectors.groupingBy(CompanyAcctRes::getChannel));
                }
                for (Map.Entry<String, List<CompanyAcctRes>> companyAcctEntry : channelInfoMap.entrySet()) {
                    FxCompanyAcctInfos fxCompanyAcctInfos = new FxCompanyAcctInfos();
                    String channel = companyAcctEntry.getKey();
                    List<CompanyAcctRes> value = companyAcctEntry.getValue();
                    List<FxCompanyAcctInfo> acctInfos = CopyUtils.copyList(value, FxCompanyAcctInfo.class);
                    fxCompanyAcctInfos.setAcctInfos(acctInfos);
                    fxCompanyAcctInfos.setCompanyId(companyId);
                    fxCompanyAcctInfos.setCompanyName(acctRes.get(0).getCompanyName());
                    FxAcctChannelEnum fxAcctChannelEnum = FxAcctChannelEnum.matchChannel(channel);
                    if (Objects.nonNull(fxAcctChannelEnum)) {
                        fxCompanyAcctInfos.setChannelName(fxAcctChannelEnum.getChannelName());
                        fxCompanyAcctInfos.setChannel(channel);
                        fxCompanyAcctInfos.setBankIconWebSmall(fxAcctChannelEnum.getBankIconWebSmall());
                        fxCompanyAcctInfos.setBankBackground(fxAcctChannelEnum.getBankBackground());
                        fxCompanyAcctInfos.setBankIconWebBig(fxAcctChannelEnum.getBankIconWebBig());
                    }
                    fxCompanyAcctInfos.setCurrencyCode(CurrencyEnum.USD.getCurrencyCode());
                    fxCompanyAcctInfos.setCurrencyName(CurrencyEnum.USD.getDisplayName());
                    fxCompanyAcctInfos.setCurrencySymbol(CurrencyEnum.USD.getSymbol());
                    fxCompanyAcctInfos.setCreateTime(acctRes.get(0).getCreateTime());
                    Optional<FxCompanyAcctInfo> accountIdOpt = acctInfos.stream().filter(v -> CurrencyEnum.USD.getCurrencyCode().equals(v.getCurrency())).findFirst();
                    if (accountIdOpt.isPresent()) {
                        fxCompanyAcctInfos.setAccountId(accountIdOpt.get().getAccountId());
                    }
                    for (FxCompanyAcctInfo acctInfo : acctInfos) {
                        fxCompanyAcctInfos.setTotalUsdBalance(fxCompanyAcctInfos.getTotalUsdBalance().add(acctInfo.getTotalBalance()));

                        // 按渠道查询虚拟卡信息
                        CardBalanceRpcReqDTO cardReq = new CardBalanceRpcReqDTO();
                        cardReq.setCompanyId(companyId);
                        cardReq.setPlatform(fxAcctChannelEnum.getChannel());
                        try {
                            List<CardBalanceRpcRespDTO> cardRes = iCardService.getBalance(cardReq);
                            if (CollectionUtils.isNotEmpty(cardRes)) {
                                acctInfo.setCardTotalBalance(cardRes.get(0).getAllBalance());
                            }
                        } catch (Exception e) {
                            FinhubLogger.warn("获取虚拟卡信息异常iCardService error cardReq:{}", JSON.toJSONString(cardReq), e);
                        }
                    }
                    fxCompanyAcctInfosList.add(fxCompanyAcctInfos);

                    // 待结算账户
                    List<CompanyAcctRes> settlementAccts = settlementAcctMap.get(companyAcctEntry.getKey());
                    if (CollectionUtils.isNotEmpty(settlementAccts)) {
                        List<FxCompanyAcctInfo> settlementAccInfos = settlementAccts.stream().map(v -> {
                            FxCompanyAcctInfo fxCompanyAcctInfo = new FxCompanyAcctInfo();
                            BeanUtils.copyProperties(v, fxCompanyAcctInfo);
                            return fxCompanyAcctInfo;
                        }).collect(Collectors.toList());
                        fxCompanyAcctInfos.setSettlementAccts(settlementAccInfos);
                    }
                }
            }
        } catch (Exception e) {
            FinhubLogger.warn("构建海外卡账户信息异常", e);
        }
        return fxCompanyAcctInfosList;
    }
}
